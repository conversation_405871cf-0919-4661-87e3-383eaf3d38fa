import os
import json
import faiss
import numpy as np
from typing import List, Dict, Any, Optional
from sentence_transformers import SentenceTransformer
import streamlit as st

class KnowledgeBase:
    """FAISS-based knowledge base for RAG functionality"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model = SentenceTransformer(model_name)
        self.dimension = 384  # Dimension for all-MiniLM-L6-v2
        
        # Separate indices for different knowledge types
        self.app_index = None
        self.test_index = None
        
        # Document storage
        self.app_documents = []
        self.test_documents = []
        
        # Storage paths
        self.storage_path = "data/knowledge_base"
        os.makedirs(self.storage_path, exist_ok=True)
        
        # Load existing indices if available
        self._load_indices()
    
    def add_documents(self, documents: List[Dict[str, Any]], doc_type: str = "app"):
        """Add documents to the knowledge base"""
        if doc_type == "app":
            self._add_to_app_knowledge(documents)
        elif doc_type == "test":
            self._add_to_test_knowledge(documents)
        else:
            raise ValueError("doc_type must be 'app' or 'test'")
    
    def _add_to_app_knowledge(self, documents: List[Dict[str, Any]]):
        """Add documents to application knowledge base"""
        # Chunk documents into smaller pieces for better retrieval
        chunks = self._chunk_documents(documents)
        
        if not chunks:
            return
        
        # Generate embeddings
        texts = [chunk['text'] for chunk in chunks]
        embeddings = self.model.encode(texts)
        
        # Initialize or update FAISS index
        if self.app_index is None:
            self.app_index = faiss.IndexFlatIP(self.dimension)
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings)
        self.app_index.add(embeddings.astype('float32'))
        
        # Store document chunks
        self.app_documents.extend(chunks)
        
        # Save to disk
        self._save_indices()
        
        st.success(f"Added {len(chunks)} chunks to application knowledge base")
    
    def _add_to_test_knowledge(self, documents: List[Dict[str, Any]]):
        """Add documents to test infrastructure knowledge base"""
        chunks = self._chunk_documents(documents)
        
        if not chunks:
            return
        
        texts = [chunk['text'] for chunk in chunks]
        embeddings = self.model.encode(texts)
        
        if self.test_index is None:
            self.test_index = faiss.IndexFlatIP(self.dimension)
        
        faiss.normalize_L2(embeddings)
        self.test_index.add(embeddings.astype('float32'))
        
        self.test_documents.extend(chunks)
        self._save_indices()
        
        st.success(f"Added {len(chunks)} chunks to test infrastructure knowledge base")
    
    def _chunk_documents(self, documents: List[Dict[str, Any]], chunk_size: int = 500, overlap: int = 50) -> List[Dict[str, Any]]:
        """Split documents into smaller chunks for better retrieval"""
        chunks = []
        
        for doc in documents:
            content = doc['content']
            words = content.split()
            
            # Create overlapping chunks
            for i in range(0, len(words), chunk_size - overlap):
                chunk_words = words[i:i + chunk_size]
                chunk_text = ' '.join(chunk_words)
                
                if len(chunk_text.strip()) > 50:  # Skip very small chunks
                    chunk = {
                        'text': chunk_text,
                        'source_filename': doc['filename'],
                        'doc_type': doc['doc_type'],
                        'chunk_id': f"{doc['filename']}_{i}"
                    }
                    chunks.append(chunk)
        
        return chunks
    
    def search(self, query: str, doc_type: str = "both", top_k: int = 5) -> List[Dict[str, Any]]:
        """Search the knowledge base for relevant documents"""
        query_embedding = self.model.encode([query])
        faiss.normalize_L2(query_embedding)
        
        results = []
        
        if doc_type in ["app", "both"] and self.app_index is not None:
            app_results = self._search_index(query_embedding, self.app_index, self.app_documents, top_k)
            results.extend(app_results)
        
        if doc_type in ["test", "both"] and self.test_index is not None:
            test_results = self._search_index(query_embedding, self.test_index, self.test_documents, top_k)
            results.extend(test_results)
        
        # Sort by relevance score and return top_k
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:top_k]
    
    def _search_index(self, query_embedding: np.ndarray, index: faiss.Index, documents: List[Dict], top_k: int) -> List[Dict[str, Any]]:
        """Search a specific FAISS index"""
        if index.ntotal == 0:
            return []
        
        scores, indices = index.search(query_embedding.astype('float32'), min(top_k, index.ntotal))
        
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx < len(documents):
                result = documents[idx].copy()
                result['score'] = float(score)
                results.append(result)
        
        return results
    
    def get_context_for_query(self, query: str, doc_type: str = "both", max_context_length: int = 2000) -> str:
        """Get relevant context for a query, formatted for LLM consumption"""
        relevant_docs = self.search(query, doc_type)
        
        context_parts = []
        current_length = 0
        
        for doc in relevant_docs:
            doc_text = f"Source: {doc['source_filename']}\n{doc['text']}\n---\n"
            
            if current_length + len(doc_text) > max_context_length:
                break
            
            context_parts.append(doc_text)
            current_length += len(doc_text)
        
        return "\n".join(context_parts)
    
    def _save_indices(self):
        """Save FAISS indices and documents to disk"""
        try:
            # Save FAISS indices
            if self.app_index is not None:
                faiss.write_index(self.app_index, os.path.join(self.storage_path, "app_index.faiss"))
            
            if self.test_index is not None:
                faiss.write_index(self.test_index, os.path.join(self.storage_path, "test_index.faiss"))
            
            # Save document metadata
            with open(os.path.join(self.storage_path, "app_documents.json"), 'w') as f:
                json.dump(self.app_documents, f, indent=2)
            
            with open(os.path.join(self.storage_path, "test_documents.json"), 'w') as f:
                json.dump(self.test_documents, f, indent=2)
                
        except Exception as e:
            st.error(f"Error saving knowledge base: {str(e)}")
    
    def _load_indices(self):
        """Load existing FAISS indices and documents from disk"""
        try:
            app_index_path = os.path.join(self.storage_path, "app_index.faiss")
            test_index_path = os.path.join(self.storage_path, "test_index.faiss")
            
            if os.path.exists(app_index_path):
                self.app_index = faiss.read_index(app_index_path)
            
            if os.path.exists(test_index_path):
                self.test_index = faiss.read_index(test_index_path)
            
            # Load document metadata
            app_docs_path = os.path.join(self.storage_path, "app_documents.json")
            if os.path.exists(app_docs_path):
                with open(app_docs_path, 'r') as f:
                    self.app_documents = json.load(f)
            
            test_docs_path = os.path.join(self.storage_path, "test_documents.json")
            if os.path.exists(test_docs_path):
                with open(test_docs_path, 'r') as f:
                    self.test_documents = json.load(f)
                    
        except Exception as e:
            st.warning(f"Could not load existing knowledge base: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get knowledge base statistics"""
        return {
            'app_documents': len(self.app_documents),
            'test_documents': len(self.test_documents),
            'app_index_size': self.app_index.ntotal if self.app_index else 0,
            'test_index_size': self.test_index.ntotal if self.test_index else 0
        }