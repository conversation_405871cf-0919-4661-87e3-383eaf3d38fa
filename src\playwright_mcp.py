import json
import subprocess
import asyncio
import os
import signal
import time
import threading
from typing import Dict, Any, List, Optional
import streamlit as st

class PlaywrightMCPClient:
    """Client for interacting with Playwright MCP server using subprocess communication"""
    
    def __init__(self):
        self.mcp_process = None
        self.test_results = []
        self.available_tools = [
            'browser_navigate', 'browser_click', 'browser_type', 
            'browser_snapshot', 'browser_wait_for', 'browser_close'
        ]
        self.server_running = False
        self.request_id = 0
    
    def start_mcp_server(self) -> bool:
        """Start the Playwright MCP server"""
        try:
            # Check if Node.js is available
            node_check = subprocess.run(['node', '--version'], 
                                      capture_output=True, text=True, timeout=5)
            if node_check.returncode != 0:
                st.error("Node.js is not installed. Please install Node.js 18+ to use Playwright MCP.")
                return False
            
            # Start the MCP server process with stdio communication
            self.mcp_process = subprocess.Popen(
                ['npx', '@playwright/mcp@latest', '--headless'],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0
            )
            
            # Wait a moment for server to start
            time.sleep(2)
            
            # Check if process is still running
            if self.mcp_process.poll() is None:
                self.server_running = True
                
                # Send initialization request
                init_success = self._send_mcp_request('initialize', {
                    'protocolVersion': '2024-11-05',
                    'capabilities': {},
                    'clientInfo': {
                        'name': 'agentic-test-automation',
                        'version': '1.0.0'
                    }
                })
                
                return init_success
            else:
                st.error("MCP server failed to start")
                return False
            
        except Exception as e:
            st.error(f"Failed to start MCP server: {str(e)}")
            self.server_running = False
            return False
    
    def check_mcp_server(self) -> bool:
        """Check if Playwright MCP server is running"""
        return (self.server_running and 
                self.mcp_process is not None and 
                self.mcp_process.poll() is None)
    
    def _send_mcp_request(self, method: str, params: Dict[str, Any]) -> bool:
        """Send an MCP request to the server"""
        if not self.mcp_process or self.mcp_process.poll() is not None:
            return False
        
        try:
            self.request_id += 1
            request = {
                'jsonrpc': '2.0',
                'id': self.request_id,
                'method': method,
                'params': params
            }
            
            request_json = json.dumps(request) + '\n'
            self.mcp_process.stdin.write(request_json)
            self.mcp_process.stdin.flush()
            
            # Read response (simplified - in practice you'd handle this better)
            response_line = self.mcp_process.stdout.readline()
            if response_line:
                response = json.loads(response_line)
                return 'error' not in response
            
            return False
            
        except Exception as e:
            st.error(f"MCP communication error: {str(e)}")
            return False
    
    def _call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call an MCP tool"""
        try:
            self.request_id += 1
            request = {
                'jsonrpc': '2.0',
                'id': self.request_id,
                'method': 'tools/call',
                'params': {
                    'name': tool_name,
                    'arguments': arguments
                }
            }
            
            request_json = json.dumps(request) + '\n'
            self.mcp_process.stdin.write(request_json)
            self.mcp_process.stdin.flush()
            
            # Read response
            response_line = self.mcp_process.stdout.readline()
            if response_line:
                response = json.loads(response_line)
                return response
            
            return {'error': 'No response from server'}
            
        except Exception as e:
            return {'error': f'Tool call failed: {str(e)}'}
    
    def execute_plain_english_test(self, test_command: str, test_name: str = "") -> Dict[str, Any]:
        """Execute a plain English test command via Playwright MCP"""
        
        if not self.check_mcp_server():
            # Try to start the server
            if not self.start_mcp_server():
                return {
                    "status": "error",
                    "message": "Playwright MCP server not available",
                    "test_name": test_name
                }
        
        try:
            # Parse the plain English command into MCP tool calls
            mcp_actions = self._parse_plain_english_to_mcp(test_command)
            
            # Execute each MCP action
            result = self._execute_mcp_actions(mcp_actions, test_name)
            self.test_results.append(result)
            return result
            
        except Exception as e:
            error_result = {
                "status": "error",
                "message": f"Test execution failed: {str(e)}",
                "test_name": test_name,
                "command": test_command
            }
            self.test_results.append(error_result)
            return error_result
    
    def _parse_plain_english_to_mcp(self, test_command: str) -> List[Dict[str, Any]]:
        """Parse plain English commands into MCP tool calls"""
        actions = []
        lines = test_command.strip().split('\n')
        
        for line in lines:
            line = line.strip().lower()
            if not line or line[0].isdigit():
                line = line.lstrip('0123456789.- ')
            
            if not line:
                continue
                
            # Navigate commands
            if 'navigate to' in line or 'go to' in line:
                url = self._extract_url_from_line(line)
                if url:
                    actions.append({
                        'tool': 'browser_navigate',
                        'arguments': {'url': url},
                        'description': f"Navigate to {url}"
                    })
            
            # Click commands
            elif 'click' in line:
                element_desc = self._extract_element_description(line)
                actions.append({
                    'tool': 'browser_click',
                    'arguments': {'element': element_desc},
                    'description': f"Click {element_desc}",
                    'requires_snapshot': True
                })
            
            # Type/Enter commands
            elif 'enter' in line or 'type' in line:
                text, element = self._extract_text_and_element(line)
                actions.append({
                    'tool': 'browser_type',
                    'arguments': {'element': element, 'text': text},
                    'description': f"Type '{text}' in {element}",
                    'requires_snapshot': True
                })
            
            # Verify/Check commands
            elif 'verify' in line or 'check' in line or 'see' in line:
                actions.append({
                    'tool': 'browser_snapshot',
                    'arguments': {},
                    'description': f"Verify: {line}",
                    'verification': line
                })
        
        return actions
    
    def _extract_url_from_line(self, line: str) -> Optional[str]:
        """Extract URL from navigation command"""
        # Look for common URL patterns
        import re
        url_pattern = r'https?://[^\s]+'
        match = re.search(url_pattern, line)
        if match:
            return match.group()
        
        # Look for quoted URLs or page names
        if '"' in line:
            parts = line.split('"')
            if len(parts) >= 2:
                return parts[1]
        
        # Default patterns
        if 'login page' in line:
            return 'https://example.com/login'
        elif 'dashboard' in line:
            return 'https://example.com/dashboard'
        
        return 'https://example.com'
    
    def _extract_element_description(self, line: str) -> str:
        """Extract element description from command"""
        # Look for quoted elements
        if '"' in line:
            parts = line.split('"')
            for part in parts:
                if 'button' in part or 'link' in part or 'field' in part:
                    return part.strip()
        
        # Common patterns
        if 'login button' in line:
            return 'login button'
        elif 'submit button' in line:
            return 'submit button'
        elif 'button' in line:
            return 'button'
        
        return 'element'
    
    def _extract_text_and_element(self, line: str) -> tuple[str, str]:
        """Extract text to type and target element"""
        text = ""
        element = "input field"
        
        # Look for quoted text
        if '"' in line:
            parts = line.split('"')
            if len(parts) >= 2:
                text = parts[1]
        
        # Identify element type
        if 'username' in line or 'email' in line:
            element = 'username field'
        elif 'password' in line:
            element = 'password field'
        elif 'field' in line:
            element = 'input field'
        
        return text, element
    
    def _execute_mcp_actions(self, actions: List[Dict[str, Any]], test_name: str) -> Dict[str, Any]:
        """Execute a list of MCP actions"""
        start_time = time.time()
        executed_steps = []
        
        try:
            for i, action in enumerate(actions):
                step_start = time.time()
                
                # Take snapshot if required for element interaction
                if action.get('requires_snapshot'):
                    snapshot_result = self._call_mcp_tool('browser_snapshot', {})
                    
                    # In a real implementation, you'd parse the snapshot to find element refs
                    # For now, we'll simulate finding the element
                    if 'arguments' in action and 'element' in action['arguments']:
                        action['arguments']['ref'] = f"element_{i}"
                
                # Execute the MCP tool call
                result = self._call_mcp_tool(action['tool'], action['arguments'])
                
                step_duration = time.time() - step_start
                
                is_success = 'error' not in result
                executed_steps.append({
                    'step': action['description'],
                    'status': 'passed' if is_success else 'failed',
                    'duration': round(step_duration, 2),
                    'result': result.get('result', {}) if is_success else result.get('error', 'Unknown error')
                })
                
                # If this is a verification step, check the result
                if 'verification' in action:
                    # Simple verification logic - in practice, you'd parse the snapshot
                    verification_passed = self._verify_condition(action['verification'], result)
                    if not verification_passed:
                        executed_steps[-1]['status'] = 'failed'
                        executed_steps[-1]['error'] = 'Verification failed'
                        break
                
                if not is_success:
                    break
            
            # Determine overall test result
            total_time = time.time() - start_time
            failed_steps = [s for s in executed_steps if s['status'] == 'failed']
            
            return {
                'status': 'failed' if failed_steps else 'passed',
                'test_name': test_name,
                'execution_time': round(total_time, 2),
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'details': executed_steps,
                'error_message': failed_steps[0].get('error') if failed_steps else None
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'test_name': test_name,
                'execution_time': round(time.time() - start_time, 2),
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'details': executed_steps,
                'error_message': f"Execution error: {str(e)}"
            }
    
    def _verify_condition(self, verification_text: str, snapshot_result) -> bool:
        """Verify a condition against the page snapshot"""
        # Simple verification logic - in practice, you'd parse the accessibility tree
        # This is a placeholder implementation
        if 'dashboard' in verification_text.lower():
            return True  # Assume dashboard verification passes
        elif 'error' in verification_text.lower():
            return True  # Assume error message verification passes
        return True
    
    def _simulate_mcp_execution(self, test_command: str, test_name: str) -> Dict[str, Any]:
        """Simulate MCP execution for demo purposes"""
        
        # Parse the test command to extract steps
        steps = self._parse_test_steps(test_command)
        
        # Simulate execution results
        import random
        import time
        
        # Simulate execution time
        execution_time = random.uniform(2, 8)
        
        # Simulate success/failure based on test content
        success_probability = 0.8
        if "invalid" in test_command.lower() or "error" in test_command.lower():
            success_probability = 0.3
        
        is_success = random.random() < success_probability
        
        result = {
            "status": "passed" if is_success else "failed",
            "test_name": test_name or "Unnamed Test",
            "command": test_command,
            "steps": steps,
            "execution_time": round(execution_time, 2),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "details": self._generate_execution_details(steps, is_success)
        }
        
        if not is_success:
            result["error_message"] = self._generate_error_message(test_command)
        
        return result
    
    def _parse_test_steps(self, test_command: str) -> List[Dict[str, str]]:
        """Parse test command into individual steps"""
        lines = test_command.strip().split('\n')
        steps = []
        
        for line in lines:
            line = line.strip()
            if line and (line[0].isdigit() or line.startswith('-')):
                # Remove numbering or bullet points
                step_text = line.lstrip('0123456789.- ')
                if step_text:
                    steps.append({
                        "step": step_text,
                        "status": "pending"
                    })
        
        return steps
    
    def _generate_execution_details(self, steps: List[Dict[str, str]], is_success: bool) -> List[Dict[str, str]]:
        """Generate detailed execution results for each step"""
        import random
        
        detailed_steps = []
        
        for i, step in enumerate(steps):
            # Most steps pass, but if test fails, one step should fail
            step_success = True
            if not is_success and i == len(steps) - 2:  # Fail near the end
                step_success = False
            
            detailed_step = {
                "step": step["step"],
                "status": "passed" if step_success else "failed",
                "duration": round(random.uniform(0.5, 2.0), 2)
            }
            
            if not step_success:
                detailed_step["error"] = "Element not found or assertion failed"
            
            detailed_steps.append(detailed_step)
        
        return detailed_steps
    
    def _generate_error_message(self, test_command: str) -> str:
        """Generate realistic error messages based on test content"""
        if "login" in test_command.lower():
            return "Login failed: Invalid credentials or element not found"
        elif "click" in test_command.lower():
            return "Element not clickable or not found on page"
        elif "verify" in test_command.lower():
            return "Assertion failed: Expected element or text not found"
        else:
            return "Test execution failed: Unexpected error occurred"
    
    def execute_test_suite(self, plain_english_tests: str) -> List[Dict[str, Any]]:
        """Execute a complete test suite"""
        
        # Parse the plain English tests into individual tests
        tests = self._parse_test_suite(plain_english_tests)
        
        results = []
        for test in tests:
            result = self.execute_plain_english_test(test["commands"], test["name"])
            results.append(result)
        
        return results
    
    def _parse_test_suite(self, plain_english_tests: str) -> List[Dict[str, str]]:
        """Parse plain English test suite into individual tests"""
        tests = []
        current_test = None
        current_commands = []
        
        lines = plain_english_tests.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('Test:'):
                # Save previous test if exists
                if current_test:
                    tests.append({
                        "name": current_test,
                        "commands": '\n'.join(current_commands)
                    })
                
                # Start new test
                current_test = line.replace('Test:', '').strip()
                current_commands = []
            
            elif line and current_test:
                current_commands.append(line)
        
        # Add the last test
        if current_test:
            tests.append({
                "name": current_test,
                "commands": '\n'.join(current_commands)
            })
        
        return tests
    
    def get_test_results_summary(self) -> Dict[str, Any]:
        """Get summary of all test results"""
        if not self.test_results:
            return {
                "total_tests": 0,
                "passed": 0,
                "failed": 0,
                "pass_rate": 0.0
            }
        
        total_tests = len(self.test_results)
        passed = sum(1 for result in self.test_results if result["status"] == "passed")
        failed = total_tests - passed
        pass_rate = (passed / total_tests) * 100 if total_tests > 0 else 0
        
        return {
            "total_tests": total_tests,
            "passed": passed,
            "failed": failed,
            "pass_rate": round(pass_rate, 2),
            "total_execution_time": sum(result.get("execution_time", 0) for result in self.test_results)
        }
    
    def generate_test_report(self) -> str:
        """Generate a comprehensive test report"""
        summary = self.get_test_results_summary()
        
        report = f"""
# Test Execution Report

## Summary
- **Total Tests**: {summary['total_tests']}
- **Passed**: {summary['passed']}
- **Failed**: {summary['failed']}
- **Pass Rate**: {summary['pass_rate']}%
- **Total Execution Time**: {summary.get('total_execution_time', 0):.2f} seconds

## Detailed Results

"""
        
        for i, result in enumerate(self.test_results, 1):
            status_emoji = "✅" if result["status"] == "passed" else "❌"
            report += f"### {i}. {result['test_name']} {status_emoji}\n"
            report += f"- **Status**: {result['status'].upper()}\n"
            report += f"- **Execution Time**: {result.get('execution_time', 0):.2f}s\n"
            
            if result["status"] == "failed":
                report += f"- **Error**: {result.get('error_message', 'Unknown error')}\n"
            
            if "details" in result:
                report += "- **Step Details**:\n"
                for step in result["details"]:
                    step_emoji = "✅" if step["status"] == "passed" else "❌"
                    report += f"  - {step_emoji} {step['step']} ({step['duration']}s)\n"
            
            report += "\n"
        
        return report
    
    def cleanup(self):
        """Clean up MCP server connection"""
        try:
            if self.mcp_process:
                self.mcp_process.terminate()
                self.mcp_process.wait(timeout=5)
                self.mcp_process = None
            
            self.server_running = False
        except Exception as e:
            st.warning(f"Cleanup warning: {str(e)}")
    
    def clear_results(self):
        """Clear all test results"""
        self.test_results = []