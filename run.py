#!/usr/bin/env python3
"""
Simple run script for the Agentic Test Automation Platform
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if requirements are installed"""
    try:
        import streamlit
        import faiss
        import groq
        return True
    except ImportError as e:
        print(f"❌ Missing requirements: {e}")
        print("Run: python setup.py")
        return False

def check_api_key():
    """Check if API key is configured"""
    if not os.getenv("GROQ_API_KEY"):
        print("⚠️  GROQ_API_KEY not found in environment")
        
        # Check for .env file
        env_file = Path(".env")
        if env_file.exists():
            print("📄 Found .env file - make sure GROQ_API_KEY is set there")
        else:
            print("💡 Create a .env file with: GROQ_API_KEY=your_api_key")
        
        return False
    return True

def main():
    """Main run function"""
    print("🚀 Starting Agentic Test Automation Platform")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check API key
    if not check_api_key():
        print("⚠️  Continuing without API key - AI features will be limited")
    
    # Create data directories if they don't exist
    data_dirs = ["data", "data/knowledge_base", "data/processed_docs", "data/reports"]
    for directory in data_dirs:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Environment check completed")
    print("🌐 Starting Streamlit application...")
    print("📱 Open your browser to: http://localhost:8501")
    print("🛑 Press Ctrl+C to stop the application")
    print("=" * 50)
    
    # Run Streamlit
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()