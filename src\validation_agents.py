"""
Validation Agents for Quality Assurance using LLM as Judge
"""

import json
import time
from typing import Dict, Any, List
from groq import Groq
import streamlit as st
from .prompt_templates import PromptTemplates

class ValidationAgent:
    """Base class for validation agents using LLM as judge"""
    
    def __init__(self, api_key: str = None, model: str = "moonshotai/kimi-k2-instruct"):
        self.client = Groq(api_key=api_key)
        self.model = model
        self.prompt_templates = PromptTemplates()
    
    def validate_content(self, content: str, validation_type: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Validate content using LLM as judge
        
        Args:
            content: Content to validate
            validation_type: Type of validation (test_plan, bdd_scenarios, plain_english_tests)
            context: Additional context for validation
            
        Returns:
            Validation results with scores and recommendations
        """
        try:
            # Get validation prompt
            validation_prompt = self.prompt_templates.get_validation_agent_prompt(validation_type)
            
            # Format prompt with content
            formatted_prompt = validation_prompt.format(
                content_to_validate=content,
                validation_type=validation_type
            )
            
            # Call LLM for validation
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": formatted_prompt}],
                temperature=0.3,  # Lower temperature for more consistent validation
                max_tokens=2000
            )
            
            # Parse JSON response
            validation_result = self._parse_validation_response(response.choices[0].message.content)
            
            # Add metadata
            validation_result["validation_metadata"] = {
                "content_length": len(content),
                "validation_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "validation_type": validation_type,
                "model_used": self.model
            }
            
            return validation_result
            
        except Exception as e:
            return self._create_error_response(str(e), validation_type)
    
    def _parse_validation_response(self, response_text: str) -> Dict[str, Any]:
        """Parse LLM response into structured validation result"""
        try:
            # Try to extract JSON from response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # Fallback: create structured response from text
                return self._create_fallback_response(response_text)
                
        except json.JSONDecodeError:
            return self._create_fallback_response(response_text)
    
    def _create_fallback_response(self, response_text: str) -> Dict[str, Any]:
        """Create fallback validation response when JSON parsing fails"""
        return {
            "validation_summary": {
                "overall_score": 75,  # Default moderate score
                "validation_type": "fallback",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "validator": "LLM Quality Judge (Fallback)"
            },
            "detailed_assessment": {
                "accuracy": {"score": 75, "findings": ["Manual review recommended"], "recommendations": []},
                "completeness": {"score": 75, "findings": ["Manual review recommended"], "recommendations": []},
                "consistency": {"score": 75, "findings": ["Manual review recommended"], "recommendations": []},
                "standards_compliance": {"score": 75, "findings": ["Manual review recommended"], "recommendations": []}
            },
            "critical_issues": [],
            "improvement_suggestions": [
                {
                    "priority": "Medium",
                    "area": "Validation Process",
                    "suggestion": "Manual review recommended due to parsing issues",
                    "expected_benefit": "Ensure quality standards are met"
                }
            ],
            "approval_status": {
                "recommended_action": "Revise",
                "conditions": ["Manual review required"],
                "next_steps": ["Review validation output", "Rerun validation if needed"]
            },
            "raw_response": response_text
        }
    
    def _create_error_response(self, error_message: str, validation_type: str) -> Dict[str, Any]:
        """Create error response for validation failures"""
        return {
            "validation_summary": {
                "overall_score": 0,
                "validation_type": validation_type,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "validator": "LLM Quality Judge (Error)"
            },
            "error": {
                "message": error_message,
                "type": "validation_error"
            },
            "approval_status": {
                "recommended_action": "Reject",
                "conditions": ["Validation failed"],
                "next_steps": ["Fix validation error", "Retry validation"]
            }
        }

class TestPlanValidator(ValidationAgent):
    """Specialized validator for test plans"""
    
    def validate_test_plan(self, test_plan: str, prd_analysis: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Validate test plan against PRD requirements and industry standards
        
        Args:
            test_plan: Generated test plan content
            prd_analysis: Original PRD analysis for traceability check
            
        Returns:
            Detailed validation results
        """
        context = {"prd_analysis": prd_analysis} if prd_analysis else {}
        return self.validate_content(test_plan, "test_plan", context)
    
    def check_requirements_coverage(self, test_plan: str, requirements: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check if test plan covers all requirements"""
        coverage_analysis = {
            "total_requirements": len(requirements),
            "covered_requirements": [],
            "missing_requirements": [],
            "coverage_percentage": 0
        }
        
        # Simple keyword-based coverage check (can be enhanced with NLP)
        test_plan_lower = test_plan.lower()
        
        for req in requirements:
            req_id = req.get("id", "")
            req_title = req.get("title", "").lower()
            
            # Check if requirement is mentioned in test plan
            if req_id.lower() in test_plan_lower or any(word in test_plan_lower for word in req_title.split()):
                coverage_analysis["covered_requirements"].append(req)
            else:
                coverage_analysis["missing_requirements"].append(req)
        
        coverage_analysis["coverage_percentage"] = (
            len(coverage_analysis["covered_requirements"]) / len(requirements) * 100
            if requirements else 0
        )
        
        return coverage_analysis

class BDDScenariosValidator(ValidationAgent):
    """Specialized validator for BDD scenarios"""
    
    def validate_bdd_scenarios(self, scenarios: str, test_plan: str = None) -> Dict[str, Any]:
        """
        Validate BDD scenarios for Gherkin syntax and completeness
        
        Args:
            scenarios: Generated BDD scenarios
            test_plan: Original test plan for traceability
            
        Returns:
            Detailed validation results
        """
        context = {"test_plan": test_plan} if test_plan else {}
        return self.validate_content(scenarios, "bdd_scenarios", context)
    
    def check_gherkin_syntax(self, scenarios: str) -> Dict[str, Any]:
        """Check Gherkin syntax compliance"""
        syntax_issues = []
        lines = scenarios.split('\n')
        
        # Basic Gherkin syntax checks
        feature_found = False
        scenario_found = False
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            if line.startswith('Feature:'):
                feature_found = True
            elif line.startswith('Scenario:') or line.startswith('Scenario Outline:'):
                scenario_found = True
            elif line.startswith(('Given', 'When', 'Then', 'And', 'But')):
                if not scenario_found:
                    syntax_issues.append(f"Line {i}: Step without scenario context")
            elif line.startswith('Examples:'):
                if 'Scenario Outline:' not in scenarios:
                    syntax_issues.append(f"Line {i}: Examples without Scenario Outline")
        
        if not feature_found:
            syntax_issues.append("Missing Feature declaration")
        if not scenario_found:
            syntax_issues.append("No scenarios found")
        
        return {
            "syntax_valid": len(syntax_issues) == 0,
            "issues": syntax_issues,
            "total_issues": len(syntax_issues)
        }

class PlainEnglishTestValidator(ValidationAgent):
    """Specialized validator for plain English tests"""
    
    def validate_plain_english_tests(self, tests: str, bdd_scenarios: str = None) -> Dict[str, Any]:
        """
        Validate plain English tests for Playwright MCP compatibility
        
        Args:
            tests: Generated plain English tests
            bdd_scenarios: Original BDD scenarios for traceability
            
        Returns:
            Detailed validation results
        """
        context = {"bdd_scenarios": bdd_scenarios} if bdd_scenarios else {}
        return self.validate_content(tests, "plain_english_tests", context)
    
    def check_playwright_compatibility(self, tests: str) -> Dict[str, Any]:
        """Check compatibility with Playwright MCP tools"""
        compatibility_issues = []
        supported_actions = [
            'navigate', 'click', 'type', 'enter', 'verify', 'check', 'see',
            'wait', 'select', 'upload', 'download', 'scroll'
        ]
        
        lines = tests.split('\n')
        test_steps = [line.strip() for line in lines if line.strip() and not line.startswith('Test:')]
        
        for i, step in enumerate(test_steps, 1):
            step_lower = step.lower()
            
            # Check if step contains supported actions
            has_supported_action = any(action in step_lower for action in supported_actions)
            
            if not has_supported_action and len(step) > 10:  # Ignore short lines
                compatibility_issues.append(f"Step {i}: '{step}' - No recognized Playwright action")
        
        return {
            "compatibility_score": max(0, 100 - (len(compatibility_issues) * 10)),
            "issues": compatibility_issues,
            "total_steps": len(test_steps),
            "problematic_steps": len(compatibility_issues)
        }

class ValidationOrchestrator:
    """Orchestrates multiple validation agents for comprehensive quality assurance"""
    
    def __init__(self, api_key: str = None):
        self.test_plan_validator = TestPlanValidator(api_key)
        self.bdd_validator = BDDScenariosValidator(api_key)
        self.plain_english_validator = PlainEnglishTestValidator(api_key)
    
    def validate_complete_workflow(self, 
                                 test_plan: str, 
                                 bdd_scenarios: str, 
                                 plain_english_tests: str,
                                 prd_analysis: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Validate complete test automation workflow
        
        Returns:
            Comprehensive validation results for all components
        """
        validation_results = {
            "workflow_validation": {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "components_validated": 3,
                "overall_status": "pending"
            },
            "test_plan_validation": {},
            "bdd_scenarios_validation": {},
            "plain_english_tests_validation": {},
            "workflow_recommendations": []
        }
        
        try:
            # Validate test plan
            st.info("🔍 Validating test plan...")
            validation_results["test_plan_validation"] = self.test_plan_validator.validate_test_plan(
                test_plan, prd_analysis
            )
            
            # Validate BDD scenarios
            st.info("🔍 Validating BDD scenarios...")
            validation_results["bdd_scenarios_validation"] = self.bdd_validator.validate_bdd_scenarios(
                bdd_scenarios, test_plan
            )
            
            # Validate plain English tests
            st.info("🔍 Validating plain English tests...")
            validation_results["plain_english_tests_validation"] = self.plain_english_validator.validate_plain_english_tests(
                plain_english_tests, bdd_scenarios
            )
            
            # Calculate overall workflow score
            scores = []
            for validation_key in ["test_plan_validation", "bdd_scenarios_validation", "plain_english_tests_validation"]:
                validation_data = validation_results[validation_key]
                if "validation_summary" in validation_data:
                    scores.append(validation_data["validation_summary"].get("overall_score", 0))
            
            overall_score = sum(scores) / len(scores) if scores else 0
            validation_results["workflow_validation"]["overall_score"] = overall_score
            
            # Determine overall status
            if overall_score >= 90:
                validation_results["workflow_validation"]["overall_status"] = "excellent"
            elif overall_score >= 75:
                validation_results["workflow_validation"]["overall_status"] = "good"
            elif overall_score >= 60:
                validation_results["workflow_validation"]["overall_status"] = "acceptable"
            else:
                validation_results["workflow_validation"]["overall_status"] = "needs_improvement"
            
            # Generate workflow recommendations
            validation_results["workflow_recommendations"] = self._generate_workflow_recommendations(
                validation_results
            )
            
        except Exception as e:
            validation_results["workflow_validation"]["error"] = str(e)
            validation_results["workflow_validation"]["overall_status"] = "error"
        
        return validation_results
    
    def _generate_workflow_recommendations(self, validation_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        # Analyze each component's validation results
        for component in ["test_plan_validation", "bdd_scenarios_validation", "plain_english_tests_validation"]:
            validation_data = validation_results.get(component, {})
            
            if "critical_issues" in validation_data:
                for issue in validation_data["critical_issues"]:
                    recommendations.append({
                        "component": component.replace("_validation", ""),
                        "priority": issue.get("severity", "Medium"),
                        "issue": issue.get("description", ""),
                        "recommendation": issue.get("recommendation", ""),
                        "type": "critical_issue"
                    })
            
            if "improvement_suggestions" in validation_data:
                for suggestion in validation_data["improvement_suggestions"][:2]:  # Limit to top 2
                    recommendations.append({
                        "component": component.replace("_validation", ""),
                        "priority": suggestion.get("priority", "Medium"),
                        "suggestion": suggestion.get("suggestion", ""),
                        "benefit": suggestion.get("expected_benefit", ""),
                        "type": "improvement"
                    })
        
        return recommendations[:10]  # Limit to top 10 recommendations