import os
import tempfile
from pathlib import Path
from typing import List, Dict, Any
import PyPDF2
from docx import Document
from pptx import Presentation
import streamlit as st

class DocumentProcessor:
    """Handles document upload and text extraction"""
    
    def __init__(self):
        self.supported_formats = ['.pdf', '.docx', '.pptx']
    
    def process_uploaded_files(self, uploaded_files, doc_type: str = "app") -> List[Dict[str, Any]]:
        """
        Process uploaded files and extract text content
        
        Args:
            uploaded_files: Streamlit uploaded files
            doc_type: 'app' for application docs, 'test' for test infrastructure docs
            
        Returns:
            List of processed documents with metadata
        """
        processed_docs = []
        
        for uploaded_file in uploaded_files:
            try:
                # Create temporary file
                with tempfile.NamedTemporaryFile(delete=False, suffix=Path(uploaded_file.name).suffix) as tmp_file:
                    tmp_file.write(uploaded_file.getvalue())
                    tmp_file_path = tmp_file.name
                
                # Extract text based on file type
                text_content = self._extract_text(tmp_file_path, uploaded_file.name)
                
                if text_content:
                    doc_metadata = {
                        'filename': uploaded_file.name,
                        'content': text_content,
                        'doc_type': doc_type,
                        'file_size': len(uploaded_file.getvalue()),
                        'file_type': Path(uploaded_file.name).suffix.lower()
                    }
                    processed_docs.append(doc_metadata)
                
                # Clean up temp file
                os.unlink(tmp_file_path)
                
            except Exception as e:
                st.error(f"Error processing {uploaded_file.name}: {str(e)}")
        
        return processed_docs
    
    def _extract_text(self, file_path: str, filename: str) -> str:
        """Extract text from different file formats"""
        file_ext = Path(filename).suffix.lower()
        
        try:
            if file_ext == '.pdf':
                return self._extract_pdf_text(file_path)
            elif file_ext == '.docx':
                return self._extract_docx_text(file_path)
            elif file_ext == '.pptx':
                return self._extract_pptx_text(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_ext}")
        except Exception as e:
            st.error(f"Text extraction failed for {filename}: {str(e)}")
            return ""
    
    def _extract_pdf_text(self, file_path: str) -> str:
        """Extract text from PDF files"""
        text = ""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            raise Exception(f"PDF extraction error: {str(e)}")
        return text.strip()
    
    def _extract_docx_text(self, file_path: str) -> str:
        """Extract text from DOCX files"""
        try:
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + " "
                    text += "\n"
            
            return text.strip()
        except Exception as e:
            raise Exception(f"DOCX extraction error: {str(e)}")
    
    def _extract_pptx_text(self, file_path: str) -> str:
        """Extract text from PPTX files"""
        try:
            prs = Presentation(file_path)
            text = ""
            
            for slide in prs.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text += shape.text + "\n"
            
            return text.strip()
        except Exception as e:
            raise Exception(f"PPTX extraction error: {str(e)}")
    
    def save_processed_docs(self, processed_docs: List[Dict[str, Any]], storage_path: str = "data/processed_docs"):
        """Save processed documents to file system"""
        os.makedirs(storage_path, exist_ok=True)
        
        for doc in processed_docs:
            doc_filename = f"{doc['doc_type']}_{doc['filename']}.txt"
            doc_path = os.path.join(storage_path, doc_filename)
            
            with open(doc_path, 'w', encoding='utf-8') as f:
                f.write(f"Filename: {doc['filename']}\n")
                f.write(f"Type: {doc['doc_type']}\n")
                f.write(f"File Type: {doc['file_type']}\n")
                f.write(f"Size: {doc['file_size']} bytes\n")
                f.write("-" * 50 + "\n")
                f.write(doc['content'])
        
        return len(processed_docs)