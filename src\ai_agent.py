import os
from typing import Dict, Any, List
from groq import Groq
import streamlit as st
from .simple_knowledge_base import SimpleKnowledgeBase as KnowledgeBase
from .prompt_templates import PromptTemplates
from .validation_agents import ValidationOrchestrator

class AIAgent:
    """AI Agent for generating test plans, scenarios, and plain English tests"""
    
    def __init__(self, api_key: str = None):
        self.client = Groq(api_key=api_key or os.getenv("GROQ_API_KEY"))
        self.model = "moonshotai/kimi-k2-instruct"
        self.knowledge_base = None
        self.prompt_templates = PromptTemplates()
        self.validation_orchestrator = ValidationOrchestrator(api_key)
    
    def set_knowledge_base(self, knowledge_base: KnowledgeBase):
        """Set the knowledge base for RAG functionality"""
        self.knowledge_base = knowledge_base
    
    def analyze_prd_document(self, prd_content: str) -> Dict[str, Any]:
        """
        Perform comprehensive PRD analysis using enhanced prompts
        
        Args:
            prd_content: Raw PRD document content
            
        Returns:
            Structured PRD analysis with requirements, user stories, and recommendations
        """
        try:
            # Get enhanced PRD analysis prompt
            analysis_prompt = self.prompt_templates.get_prd_analysis_prompt()
            
            # Format prompt with PRD content
            formatted_prompt = analysis_prompt.format(prd_content=prd_content)
            
            # Call LLM for analysis
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": formatted_prompt}],
                temperature=0.3,  # Lower temperature for more structured analysis
                max_tokens=3000
            )
            
            # Parse JSON response
            analysis_result = self._parse_json_response(response.choices[0].message.content)
            
            # Add metadata
            analysis_result["analysis_metadata"] = {
                "prd_length": len(prd_content),
                "analysis_timestamp": self._get_timestamp(),
                "model_used": self.model
            }
            
            return analysis_result
            
        except Exception as e:
            st.error(f"PRD analysis failed: {str(e)}")
            return self._create_fallback_prd_analysis(prd_content)
    
    def _parse_json_response(self, response_text: str) -> Dict[str, Any]:
        """Parse JSON response from LLM"""
        import json
        try:
            # Find JSON in response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                raise ValueError("No JSON found in response")
                
        except (json.JSONDecodeError, ValueError):
            # Return fallback structure
            return {
                "document_metadata": {"title": "Unknown", "version": "1.0"},
                "project_overview": {"purpose": "Analysis failed", "scope": "Unknown"},
                "functional_requirements": [],
                "non_functional_requirements": [],
                "user_stories": [],
                "test_strategy_recommendations": {"critical_test_areas": [], "automation_candidates": []},
                "identified_gaps": [],
                "out_of_scope": [],
                "raw_response": response_text
            }
    
    def _create_fallback_prd_analysis(self, prd_content: str) -> Dict[str, Any]:
        """Create fallback PRD analysis when parsing fails"""
        return {
            "document_metadata": {
                "title": "PRD Analysis (Fallback)",
                "version": "1.0",
                "analysis_status": "fallback"
            },
            "project_overview": {
                "purpose": "Manual analysis required",
                "scope": "To be determined",
                "business_context": "Analysis failed - manual review needed"
            },
            "functional_requirements": [
                {
                    "id": "FR-001",
                    "title": "Manual Analysis Required",
                    "description": "PRD analysis failed - manual review needed",
                    "priority": "High",
                    "complexity": "Unknown"
                }
            ],
            "analysis_metadata": {
                "prd_length": len(prd_content),
                "analysis_timestamp": self._get_timestamp(),
                "status": "fallback"
            }
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        import time
        return time.strftime("%Y-%m-%d %H:%M:%S")
    
    def generate_test_plan(self, user_story: str, prd_analysis: Dict[str, Any] = None) -> str:
        """Generate a comprehensive test plan using enhanced prompts"""
        
        # Get relevant context from knowledge base
        knowledge_context = ""
        if self.knowledge_base:
            app_context = self.knowledge_base.get_context_for_query(user_story, "app", 1000)
            test_context = self.knowledge_base.get_context_for_query("test plan template standards", "test", 500)
            knowledge_context = f"Application Context:\n{app_context}\n\nTest Standards Context:\n{test_context}"
        
        # Get enhanced test plan generation prompt
        test_plan_prompt = self.prompt_templates.get_test_plan_generation_prompt()
        
        # Format prompt with context
        formatted_prompt = test_plan_prompt.format(
            prd_analysis=prd_analysis or {"user_story": user_story},
            knowledge_context=knowledge_context,
            project_name=self._extract_project_name(user_story, prd_analysis),
            project_code=self._generate_project_code(user_story),
            version="1.0",
            current_date=self._get_timestamp().split()[0]
        )
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": formatted_prompt}],
                temperature=0.6,  # Balanced creativity and consistency
                max_tokens=3000
            )
            return response.choices[0].message.content
        except Exception as e:
            st.error(f"Error generating test plan: {str(e)}")
            return "Error generating test plan. Please check your API configuration."
    
    def _extract_project_name(self, user_story: str, prd_analysis: Dict[str, Any] = None) -> str:
        """Extract project name from user story or PRD analysis"""
        if prd_analysis and "document_metadata" in prd_analysis:
            return prd_analysis["document_metadata"].get("title", "Test Project")
        
        # Simple extraction from user story
        if "login" in user_story.lower():
            return "User Authentication System"
        elif "checkout" in user_story.lower():
            return "E-commerce Checkout System"
        else:
            return "Software Application"
    
    def _generate_project_code(self, user_story: str) -> str:
        """Generate project code from user story"""
        words = user_story.split()[:3]  # Take first 3 words
        code = "".join([word[0].upper() for word in words if word.isalpha()])
        return code[:3] if len(code) >= 3 else "PRJ"
    
    def generate_bdd_scenarios(self, user_story: str, test_plan: str) -> str:
        """Generate BDD scenarios using enhanced prompts"""
        
        # Get enhanced BDD generation prompt
        bdd_prompt = self.prompt_templates.get_bdd_scenario_generation_prompt()
        
        # Format prompt with test plan
        formatted_prompt = bdd_prompt.format(test_plan=test_plan)
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": formatted_prompt}],
                temperature=0.7,
                max_tokens=3000
            )
            return response.choices[0].message.content
        except Exception as e:
            st.error(f"Error generating BDD scenarios: {str(e)}")
            return "Error generating BDD scenarios. Please check your API configuration."
    
    def convert_bdd_to_plain_english(self, bdd_scenarios: str) -> str:
        """Convert BDD scenarios to plain English commands for Playwright MCP"""
        
        context = ""
        if self.knowledge_base:
            playwright_context = self.knowledge_base.get_context_for_query("playwright automation commands", "test", 500)
            context = f"Playwright Context:\n{playwright_context}"
        
        prompt = f"""
You are an expert test automation engineer. Convert the BDD scenarios into plain English commands that can be executed by Playwright MCP.

{f"Relevant Context from Knowledge Base:\n{context}\n" if context else ""}

BDD Scenarios:
{bdd_scenarios}

Convert each scenario into a series of plain English commands that Playwright MCP can understand and execute.

Guidelines:
1. Use simple, clear English instructions
2. Be specific about UI elements (buttons, fields, links)
3. Include verification steps
4. Maintain the logical flow of the original BDD scenario
5. Group related commands under test names
6. Include setup and teardown steps where needed

Example format:
Test: [Test Name]
1. Navigate to [URL/Page]
2. Enter "[text]" in the [field name] field
3. Click the [button name] button
4. Verify that [expected result]
5. Verify that [another expected result]

Convert all scenarios following this format.
"""
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.6,
                max_tokens=2000
            )
            return response.choices[0].message.content
        except Exception as e:
            st.error(f"Error converting to plain English: {str(e)}")
            return "Error converting scenarios. Please check your API configuration."
    
    def regenerate_content(self, content_type: str, original_content: str, user_feedback: str = "") -> str:
        """Regenerate content based on user feedback"""
        
        prompts = {
            "test_plan": "Regenerate the test plan with the following improvements:",
            "bdd_scenarios": "Regenerate the BDD scenarios with the following modifications:",
            "plain_english": "Regenerate the plain English commands with the following changes:"
        }
        
        if content_type not in prompts:
            return "Invalid content type for regeneration."
        
        prompt = f"""
{prompts[content_type]}

Original Content:
{original_content}

User Feedback/Requirements:
{user_feedback if user_feedback else "Improve clarity, completeness, and accuracy"}

Please regenerate the content addressing the feedback while maintaining professional quality and following best practices.
"""
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=2000
            )
            return response.choices[0].message.content
        except Exception as e:
            st.error(f"Error regenerating content: {str(e)}")
            return original_content
    
    def analyze_user_story(self, user_story: str) -> Dict[str, Any]:
        """Analyze user story and extract key information"""
        
        prompt = f"""
Analyze the following user story and extract key information:

User Story:
{user_story}

Extract and return the following information in JSON format:
{{
    "story_type": "feature/bug/enhancement",
    "complexity": "low/medium/high",
    "test_areas": ["area1", "area2"],
    "acceptance_criteria": ["criteria1", "criteria2"],
    "risk_level": "low/medium/high",
    "estimated_test_effort": "hours/days"
}}
"""
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=500
            )
            
            # Parse JSON response
            import json
            return json.loads(response.choices[0].message.content)
        except Exception as e:
            st.warning(f"Could not analyze user story: {str(e)}")
            return {
                "story_type": "feature",
                "complexity": "medium",
                "test_areas": ["functional"],
                "acceptance_criteria": ["To be defined"],
                "risk_level": "medium",
                "estimated_test_effort": "1-2 days"
            }
    
    def validate_test_plan(self, test_plan: str, prd_analysis: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Validate generated test plan using LLM as judge
        
        Args:
            test_plan: Generated test plan content
            prd_analysis: Original PRD analysis for traceability
            
        Returns:
            Validation results with scores and recommendations
        """
        return self.validation_orchestrator.test_plan_validator.validate_test_plan(
            test_plan, prd_analysis
        )
    
    def validate_bdd_scenarios(self, bdd_scenarios: str, test_plan: str = None) -> Dict[str, Any]:
        """
        Validate generated BDD scenarios using LLM as judge
        
        Args:
            bdd_scenarios: Generated BDD scenarios
            test_plan: Original test plan for traceability
            
        Returns:
            Validation results with scores and recommendations
        """
        return self.validation_orchestrator.bdd_validator.validate_bdd_scenarios(
            bdd_scenarios, test_plan
        )
    
    def validate_plain_english_tests(self, plain_english_tests: str, bdd_scenarios: str = None) -> Dict[str, Any]:
        """
        Validate generated plain English tests using LLM as judge
        
        Args:
            plain_english_tests: Generated plain English tests
            bdd_scenarios: Original BDD scenarios for traceability
            
        Returns:
            Validation results with scores and recommendations
        """
        return self.validation_orchestrator.plain_english_validator.validate_plain_english_tests(
            plain_english_tests, bdd_scenarios
        )
    
    def validate_complete_workflow(self, 
                                 test_plan: str, 
                                 bdd_scenarios: str, 
                                 plain_english_tests: str,
                                 prd_analysis: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Validate the complete test automation workflow
        
        Returns:
            Comprehensive validation results for all components
        """
        return self.validation_orchestrator.validate_complete_workflow(
            test_plan, bdd_scenarios, plain_english_tests, prd_analysis
        )