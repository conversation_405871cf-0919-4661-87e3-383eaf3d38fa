#!/usr/bin/env python3
"""
Setup script for Agentic Test Automation Platform
"""

import os
import sys
import subprocess
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False
    return True

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    directories = [
        "data",
        "data/knowledge_base", 
        "data/processed_docs",
        "data/reports"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   Created: {directory}")
    
    print("✅ Directories created successfully!")

def check_environment():
    """Check environment setup"""
    print("🔍 Checking environment...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python version: {sys.version}")
    
    # Check Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            node_version = result.stdout.strip()
            print(f"✅ Node.js version: {node_version}")
            
            # Check if version is 18+
            version_num = int(node_version.replace('v', '').split('.')[0])
            if version_num < 18:
                print("⚠️  Node.js 18+ is recommended for Playwright MCP")
        else:
            print("❌ Node.js not found")
            print("   Please install Node.js 18+ from https://nodejs.org/")
            print("   Playwright MCP requires Node.js for browser automation")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Node.js not found or not accessible")
        print("   Please install Node.js 18+ from https://nodejs.org/")
        return False
    
    # Check for GROQ API key
    groq_key = os.getenv("GROQ_API_KEY")
    if not groq_key:
        print("⚠️  GROQ_API_KEY environment variable not set")
        print("   Please set it with: export GROQ_API_KEY=your_api_key")
        print("   Or create a .env file with GROQ_API_KEY=your_api_key")
    else:
        print("✅ GROQ API key found")
    
    return True

def create_env_template():
    """Create .env template file"""
    env_template = """# Agentic Test Automation Platform Environment Variables

# Groq API Configuration
GROQ_API_KEY=your_groq_api_key_here

# Optional: Custom model (default: moonshotai/kimi-k2-instruct)
# GROQ_MODEL=moonshotai/kimi-k2-instruct

# Optional: Playwright MCP Server URL
# MCP_SERVER_URL=localhost:8000
"""
    
    if not Path(".env").exists():
        with open(".env", "w") as f:
            f.write(env_template)
        print("✅ Created .env template file")
        print("   Please edit .env and add your GROQ API key")
    else:
        print("ℹ️  .env file already exists")

def main():
    """Main setup function"""
    print("🚀 Setting up Agentic Test Automation Platform")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Create environment template
    create_env_template()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file and add your GROQ API key")
    print("2. Run the application: streamlit run app.py")
    print("3. Open your browser to http://localhost:8501")
    print("4. Click 'Start MCP Server' in the Test Execution tab")
    print("\nPlaywright MCP will be installed automatically on first use.")
    print("The system will download @playwright/mcp@latest via npx.")

if __name__ == "__main__":
    main()