#!/usr/bin/env python3
"""
Demo script to test the Agentic Test Automation Platform components
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.knowledge_base import KnowledgeBase
from src.ai_agent import AIAgent
from src.playwright_mcp import PlaywrightMCPClient

def demo_knowledge_base():
    """Demo knowledge base functionality"""
    print("🧠 Testing Knowledge Base...")
    
    kb = KnowledgeBase()
    
    # Add sample documents
    sample_docs = [
        {
            'filename': 'login_requirements.txt',
            'content': 'The login page should have username and password fields. Users must be able to login with valid credentials and see error messages for invalid ones.',
            'doc_type': 'app',
            'file_size': 150,
            'file_type': '.txt'
        },
        {
            'filename': 'test_standards.txt', 
            'content': 'All tests should follow BDD format with Given-When-Then structure. Use appropriate tags like @smoke, @regression, @critical.',
            'doc_type': 'test',
            'file_size': 120,
            'file_type': '.txt'
        }
    ]
    
    kb.add_documents(sample_docs, "app")
    
    # Test search
    results = kb.search("login page requirements", "app", 2)
    print(f"   Found {len(results)} relevant documents")
    
    # Get context
    context = kb.get_context_for_query("login functionality", "both")
    print(f"   Generated context: {len(context)} characters")
    
    print("✅ Knowledge Base test completed!")
    return kb

def demo_ai_agent(kb):
    """Demo AI agent functionality"""
    print("\n🤖 Testing AI Agent...")
    
    # Check if API key is available
    if not os.getenv("GROQ_API_KEY"):
        print("⚠️  GROQ_API_KEY not set - skipping AI agent tests")
        return None
    
    agent = AIAgent()
    agent.set_knowledge_base(kb)
    
    sample_user_story = """
    As a user
    I want to login to the application
    So that I can access my dashboard
    
    Acceptance Criteria:
    - User can enter valid credentials
    - System validates credentials  
    - User is redirected to dashboard on success
    - Error message shown for invalid credentials
    """
    
    try:
        # Test user story analysis
        analysis = agent.analyze_user_story(sample_user_story)
        print(f"   Story analysis: {analysis['story_type']}, {analysis['complexity']} complexity")
        
        # Test test plan generation
        test_plan = agent.generate_test_plan(sample_user_story)
        print(f"   Generated test plan: {len(test_plan)} characters")
        
        # Test BDD scenario generation
        bdd_scenarios = agent.generate_bdd_scenarios(sample_user_story, test_plan[:500])
        print(f"   Generated BDD scenarios: {len(bdd_scenarios)} characters")
        
        # Test plain English conversion
        plain_english = agent.convert_bdd_to_plain_english(bdd_scenarios[:1000])
        print(f"   Generated plain English tests: {len(plain_english)} characters")
        
        print("✅ AI Agent test completed!")
        return {
            'test_plan': test_plan,
            'bdd_scenarios': bdd_scenarios,
            'plain_english': plain_english
        }
        
    except Exception as e:
        print(f"❌ AI Agent test failed: {str(e)}")
        return None

def demo_playwright_mcp(ai_results):
    """Demo Playwright MCP functionality"""
    print("\n🎭 Testing Playwright MCP...")
    
    client = PlaywrightMCPClient()
    
    # Test MCP server check
    status = client.check_mcp_server()
    print(f"   MCP server status: {'Connected' if status else 'Disconnected'}")
    
    # Test with sample plain English commands
    sample_commands = """
Test: Login with valid credentials
1. Navigate to the login page
2. Enter "testuser1" in the username field
3. Enter "password123" in the password field
4. Click the login button
5. Verify that the dashboard page is displayed

Test: Login with invalid credentials  
1. Navigate to the login page
2. Enter "<EMAIL>" in the username field
3. Enter "wrongpassword" in the password field
4. Click the login button
5. Verify that an error message is displayed
"""
    
    # Use AI-generated commands if available
    if ai_results and 'plain_english' in ai_results:
        commands = ai_results['plain_english'][:1000]  # Limit for demo
    else:
        commands = sample_commands
    
    # Execute test suite
    results = client.execute_test_suite(commands)
    print(f"   Executed {len(results)} tests")
    
    # Show summary
    summary = client.get_test_results_summary()
    print(f"   Results: {summary['passed']} passed, {summary['failed']} failed")
    print(f"   Pass rate: {summary['pass_rate']}%")
    
    print("✅ Playwright MCP test completed!")
    return results

def main():
    """Run all demos"""
    print("🚀 Agentic Test Automation Platform Demo")
    print("=" * 50)
    
    # Test knowledge base
    kb = demo_knowledge_base()
    
    # Test AI agent
    ai_results = demo_ai_agent(kb)
    
    # Test Playwright MCP
    mcp_results = demo_playwright_mcp(ai_results)
    
    print("\n" + "=" * 50)
    print("🎉 Demo completed!")
    
    if ai_results:
        print("\n📋 Sample Generated Content:")
        print("\nTest Plan (first 200 chars):")
        print(ai_results['test_plan'][:200] + "...")
        
        print("\nBDD Scenarios (first 300 chars):")
        print(ai_results['bdd_scenarios'][:300] + "...")
        
        print("\nPlain English Commands (first 200 chars):")
        print(ai_results['plain_english'][:200] + "...")
    
    if mcp_results:
        print(f"\n🎯 Test Execution Results:")
        for result in mcp_results[:2]:  # Show first 2 results
            status_emoji = "✅" if result["status"] == "passed" else "❌"
            print(f"   {status_emoji} {result['test_name']}: {result['status'].upper()}")

if __name__ == "__main__":
    main()