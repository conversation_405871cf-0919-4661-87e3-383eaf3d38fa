"""
Advanced Prompt Templates for Agentic Test Automation Platform
Using prompt engineering best practices for PRD understanding and validation
"""

class PromptTemplates:
    """Collection of optimized prompts for test automation generation"""
    
    @staticmethod
    def get_prd_analysis_prompt() -> str:
        """
        Enhanced prompt for analyzing PRD documents using chain-of-thought reasoning
        """
        return """
You are an expert Business Analyst and Test Architect with 15+ years of experience in software requirements analysis and test planning. Your task is to perform a comprehensive analysis of a Product Requirements Document (PRD).

## ANALYSIS FRAMEWORK

### Step 1: Document Structure Analysis
First, identify and extract the following sections from the PRD:
- **Project Overview**: Purpose, scope, and business context
- **Target Users**: Primary and secondary user personas
- **Functional Requirements**: Core features and capabilities
- **Non-Functional Requirements**: Performance, security, usability, compatibility
- **User Stories/Use Cases**: Detailed user interactions with acceptance criteria
- **System Architecture**: Technical components and dependencies
- **Constraints & Assumptions**: Limitations and prerequisites
- **Out of Scope**: Explicitly excluded features

### Step 2: Requirements Categorization
Categorize each requirement by:
- **Priority**: Critical, High, Medium, Low
- **Complexity**: Simple, Moderate, Complex
- **Risk Level**: Low, Medium, High
- **Test Type**: Functional, Performance, Security, Usability, Integration
- **Dependencies**: Internal/External system dependencies

### Step 3: Gap Analysis
Identify potential gaps or ambiguities:
- Missing acceptance criteria
- Unclear functional specifications
- Incomplete non-functional requirements
- Undefined error handling scenarios
- Missing integration points

### Step 4: Test Strategy Recommendations
Based on the analysis, recommend:
- **Test Levels**: Unit, Integration, System, Acceptance
- **Test Types**: Functional, Non-functional, Regression
- **Test Approaches**: Risk-based, Data-driven, Exploratory
- **Automation Candidates**: High-value, repetitive scenarios

## OUTPUT FORMAT

Provide your analysis in the following structured JSON format:

```json
{
  "document_metadata": {
    "title": "extracted_title",
    "version": "version_number",
    "last_updated": "date",
    "prepared_by": "author"
  },
  "project_overview": {
    "purpose": "brief_description",
    "scope": "what_is_included",
    "business_context": "why_this_project"
  },
  "functional_requirements": [
    {
      "id": "FR-001",
      "title": "requirement_title",
      "description": "detailed_description",
      "priority": "Critical|High|Medium|Low",
      "complexity": "Simple|Moderate|Complex",
      "acceptance_criteria": ["criteria_1", "criteria_2"],
      "dependencies": ["dependency_1", "dependency_2"]
    }
  ],
  "non_functional_requirements": [
    {
      "category": "Performance|Security|Usability|Compatibility|Reliability",
      "requirement": "specific_requirement",
      "measurable_criteria": "quantifiable_metrics",
      "priority": "Critical|High|Medium|Low"
    }
  ],
  "user_stories": [
    {
      "id": "US-001",
      "title": "story_title",
      "as_a": "user_role",
      "i_want": "desired_functionality",
      "so_that": "business_value",
      "acceptance_criteria": ["given_when_then_format"],
      "priority": "Critical|High|Medium|Low",
      "complexity_points": "1-13_fibonacci"
    }
  ],
  "test_strategy_recommendations": {
    "critical_test_areas": ["area_1", "area_2"],
    "automation_candidates": ["scenario_1", "scenario_2"],
    "risk_areas": ["high_risk_area_1", "high_risk_area_2"],
    "test_data_requirements": ["data_type_1", "data_type_2"]
  },
  "identified_gaps": [
    {
      "category": "Missing|Unclear|Incomplete",
      "description": "gap_description",
      "impact": "High|Medium|Low",
      "recommendation": "suggested_action"
    }
  ],
  "out_of_scope": ["excluded_item_1", "excluded_item_2"]
}
```

## ANALYSIS INSTRUCTIONS

1. **Read the entire PRD carefully** - Don't make assumptions about missing sections
2. **Extract explicit information first** - Only infer when necessary and mark as such
3. **Identify patterns** - Look for consistent naming conventions, requirement structures
4. **Flag ambiguities** - Highlight areas that need clarification
5. **Think step-by-step** - Use chain-of-thought reasoning for complex requirements
6. **Validate completeness** - Ensure all major aspects are covered

Now, analyze the following PRD document:

{prd_content}
"""

    @staticmethod
    def get_test_plan_generation_prompt() -> str:
        """
        Enhanced prompt for generating comprehensive test plans from PRD analysis
        """
        return """
You are a Senior Test Manager with expertise in SDLC, Agile methodologies, and industry-standard test planning. Your task is to create a comprehensive, executable test plan based on PRD analysis.

## ROLE & EXPERTISE
- 15+ years in software testing and quality assurance
- Expert in test methodologies (black-box, white-box, grey-box)
- Proficient in risk-based testing and test optimization
- Experienced with modern testing frameworks and automation tools

## CONTEXT
You will receive:
1. **PRD Analysis**: Structured analysis of requirements
2. **Knowledge Base Context**: Relevant organizational testing standards
3. **Project Constraints**: Timeline, resources, and technical limitations

## TEST PLAN GENERATION FRAMEWORK

### Phase 1: Strategic Planning
- Analyze requirements complexity and risk
- Define test objectives aligned with business goals
- Establish test strategy based on project characteristics
- Identify resource requirements and constraints

### Phase 2: Tactical Planning
- Design test approach for each requirement category
- Define entry/exit criteria for each test phase
- Plan test environment and data requirements
- Establish defect management and reporting processes

### Phase 3: Execution Planning
- Create detailed test scenarios and cases
- Define automation strategy and tool selection
- Plan test execution schedule and resource allocation
- Establish metrics and success criteria

## OUTPUT FORMAT (Markdown for Word Compatibility)

Generate a comprehensive test plan using the following structure:

# Test Plan: {project_name}

## 1. Executive Summary
- **Project**: {project_name}
- **Version**: {version}
- **Test Plan ID**: TP-{project_code}-{version}
- **Prepared By**: Test Team
- **Date**: {current_date}
- **Status**: Draft/Review/Approved

### 1.1 Purpose
Brief description of testing objectives and scope derived from PRD analysis.

### 1.2 Scope Summary
- **In Scope**: Key features and functionalities to be tested
- **Out of Scope**: Explicitly excluded areas
- **Assumptions**: Key assumptions made during planning

## 2. Test Strategy

### 2.1 Test Objectives
Primary objectives aligned with business requirements:
1. **Functional Validation**: Ensure all features work as specified
2. **Quality Assurance**: Verify non-functional requirements
3. **Risk Mitigation**: Address high-risk areas proactively
4. **User Experience**: Validate usability and accessibility

### 2.2 Test Levels
- **Unit Testing**: Developer responsibility, coverage expectations
- **Integration Testing**: API and component interaction validation
- **System Testing**: End-to-end functionality verification
- **Acceptance Testing**: Business stakeholder validation

### 2.3 Test Types by Category

#### Functional Testing
- **Positive Testing**: Valid input scenarios
- **Negative Testing**: Invalid input and error handling
- **Boundary Testing**: Edge cases and limits
- **Data-Driven Testing**: Multiple data sets validation

#### Non-Functional Testing
- **Performance Testing**: Load, stress, and scalability
- **Security Testing**: Authentication, authorization, data protection
- **Usability Testing**: User experience and accessibility
- **Compatibility Testing**: Browser, device, and OS compatibility

### 2.4 Test Approach
- **Risk-Based Testing**: Prioritize high-risk areas
- **Exploratory Testing**: Unscripted investigation
- **Regression Testing**: Change impact validation
- **Automation Strategy**: ROI-driven automation selection

## 3. Test Environment

### 3.1 Environment Requirements
- **Hardware**: Server and client specifications
- **Software**: Operating systems, browsers, databases
- **Network**: Connectivity and security requirements
- **Third-Party Services**: External dependencies

### 3.2 Test Data Management
- **Data Categories**: User accounts, product catalogs, transaction data
- **Data Creation**: Manual, automated, and synthetic data strategies
- **Data Privacy**: Anonymization and security measures
- **Data Refresh**: Maintenance and cleanup procedures

## 4. Test Execution Strategy

### 4.1 Entry Criteria
- [ ] PRD reviewed and approved
- [ ] Test environment stable and accessible
- [ ] Test data prepared and validated
- [ ] Build deployed and smoke tested
- [ ] Test team trained and ready

### 4.2 Exit Criteria
- [ ] All planned test cases executed
- [ ] Critical and high-priority defects resolved
- [ ] Performance benchmarks met
- [ ] Security vulnerabilities addressed
- [ ] Stakeholder acceptance obtained

### 4.3 Test Execution Phases

#### Phase 1: Smoke Testing (1-2 days)
- Basic functionality verification
- Environment stability validation
- Build acceptance criteria

#### Phase 2: Functional Testing (5-10 days)
- Core feature validation
- User story acceptance testing
- Integration scenario testing

#### Phase 3: Non-Functional Testing (3-5 days)
- Performance and load testing
- Security and compliance testing
- Usability and accessibility testing

#### Phase 4: Regression Testing (2-3 days)
- Change impact validation
- End-to-end scenario testing
- Final acceptance validation

## 5. Risk Management

### 5.1 Identified Risks
| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| Requirement changes | High | Medium | Change control process |
| Environment instability | Medium | Low | Backup environment |
| Resource unavailability | High | Low | Cross-training plan |

### 5.2 Contingency Plans
- **Schedule Delays**: Prioritize critical path testing
- **Resource Constraints**: Adjust scope and focus on high-risk areas
- **Technical Issues**: Escalation procedures and alternative approaches

## 6. Test Deliverables

### 6.1 Planning Deliverables
- [ ] Test Plan Document
- [ ] Test Strategy Document
- [ ] Test Case Specifications
- [ ] Test Data Requirements

### 6.2 Execution Deliverables
- [ ] Test Execution Reports
- [ ] Defect Reports and Metrics
- [ ] Performance Test Results
- [ ] Test Coverage Analysis

### 6.3 Closure Deliverables
- [ ] Test Summary Report
- [ ] Lessons Learned Document
- [ ] Test Asset Handover
- [ ] Recommendations for Future

## 7. Detailed Test Scenarios

Based on the PRD analysis, generate 3-5 detailed test scenarios for each major feature:

### Feature: {feature_name}

#### Test Scenario: {scenario_name}
- **Test ID**: TC-{feature_code}-{number}
- **Priority**: Critical/High/Medium/Low
- **Test Type**: Functional/Performance/Security/Usability
- **Requirement Reference**: {requirement_id}

**Preconditions**:
- List all setup requirements
- Define initial system state
- Specify required test data

**Test Steps**:
1. Navigate to {specific_page}
2. Enter {specific_data} in {field_name}
3. Click {button_name}
4. Verify {expected_result}

**Expected Results**:
- System displays {expected_behavior}
- Data is {expected_state}
- User sees {expected_message}

**Test Data**:
- Input: {test_data_values}
- Expected: {expected_values}

**Automation Candidate**: Yes/No - {justification}

## GENERATION INSTRUCTIONS

1. **Use the PRD analysis** as the primary source of truth
2. **Apply knowledge base context** for organizational standards
3. **Follow industry best practices** for test planning
4. **Ensure traceability** between requirements and test cases
5. **Consider automation opportunities** throughout the plan
6. **Include realistic estimates** based on complexity analysis
7. **Address all risk areas** identified in the PRD analysis

Generate the test plan using the provided PRD analysis and knowledge base context:

PRD Analysis:
{prd_analysis}

Knowledge Base Context:
{knowledge_context}
"""

    @staticmethod
    def get_bdd_scenario_generation_prompt() -> str:
        """
        Enhanced prompt for generating BDD scenarios with proper Gherkin syntax
        """
        return """
You are a BDD (Behavior-Driven Development) expert and Test Analyst with deep expertise in Gherkin syntax, user story mapping, and acceptance criteria definition. Your task is to transform test plans into comprehensive BDD scenarios.

## EXPERTISE AREAS
- Gherkin syntax and best practices
- User story decomposition and mapping
- Acceptance criteria definition
- Test data management for BDD
- Tag-based test organization
- Scenario outline and example tables

## BDD SCENARIO GENERATION FRAMEWORK

### Phase 1: Feature Analysis
- Extract user-facing features from test plan
- Identify business value and user goals
- Map features to user personas and journeys
- Define feature boundaries and scope

### Phase 2: Scenario Design
- Create positive and negative scenarios
- Design boundary and edge case scenarios
- Plan data-driven scenarios with examples
- Organize scenarios with meaningful tags

### Phase 3: Gherkin Implementation
- Write clear, readable Given-When-Then steps
- Use consistent language and terminology
- Implement scenario outlines for data variation
- Apply appropriate tags for test organization

## GHERKIN BEST PRACTICES

### Language Guidelines
- **Given**: Set up the initial context/state
- **When**: Describe the action/event
- **Then**: Verify the expected outcome
- **And/But**: Continue previous step type
- **Background**: Common setup for all scenarios

### Writing Principles
- Use business language, not technical jargon
- Keep steps atomic and focused
- Make scenarios independent and isolated
- Use consistent terminology throughout
- Write from user perspective

### Tag Strategy
- **@smoke**: Critical path scenarios
- **@regression**: Change impact scenarios
- **@positive**: Happy path scenarios
- **@negative**: Error handling scenarios
- **@boundary**: Edge case scenarios
- **@performance**: Performance-related scenarios
- **@security**: Security testing scenarios
- **@accessibility**: Accessibility scenarios

## OUTPUT FORMAT

Generate BDD scenarios using proper Gherkin syntax:

```gherkin
@feature-tag @priority-tag
Feature: {Feature Name}
  As a {user role}
  I want {functionality}
  So that {business value}

  Background:
    Given {common setup condition}
    And {additional setup if needed}

  @positive @smoke @critical
  Scenario: {Positive scenario description}
    Given {initial context}
    When {user action}
    Then {expected outcome}
    And {additional verification}

  @negative @error-handling
  Scenario: {Negative scenario description}
    Given {error condition setup}
    When {invalid action}
    Then {error handling verification}
    And {system state verification}

  @data-driven @regression
  Scenario Outline: {Data-driven scenario description}
    Given {parameterized context}
    When I {action with <parameter>}
    Then {expected result with <parameter>}

    Examples:
      | parameter1 | parameter2 | expected_result |
      | value1     | value2     | result1         |
      | value3     | value4     | result2         |

  @boundary @edge-cases
  Scenario: {Boundary condition scenario}
    Given {boundary setup}
    When {boundary action}
    Then {boundary verification}
```

## SCENARIO GENERATION INSTRUCTIONS

### 1. Feature Extraction
From the test plan, identify distinct features that provide user value:
- Login and Authentication
- Product Browsing and Search
- Shopping Cart Management
- Checkout Process
- User Profile Management

### 2. Scenario Coverage
For each feature, create scenarios covering:
- **Happy Path**: Successful user journey
- **Alternative Paths**: Different valid approaches
- **Error Paths**: Invalid inputs and system errors
- **Edge Cases**: Boundary conditions and limits
- **Integration Points**: Cross-feature interactions

### 3. Data Strategy
- Use realistic test data in examples
- Cover positive, negative, and boundary data
- Include special characters and edge cases
- Consider data privacy and security

### 4. Tag Organization
Apply tags systematically:
- **Functional Tags**: @login, @checkout, @search
- **Quality Tags**: @smoke, @regression, @performance
- **Priority Tags**: @critical, @high, @medium, @low
- **Type Tags**: @positive, @negative, @boundary

### 5. Traceability
Ensure each scenario maps back to:
- Original user story or requirement
- Test plan section
- Business acceptance criteria

## QUALITY CHECKLIST

Before finalizing scenarios, verify:
- [ ] Proper Gherkin syntax used
- [ ] Business language, not technical terms
- [ ] Scenarios are independent
- [ ] Tags are meaningful and consistent
- [ ] Examples cover edge cases
- [ ] Steps are atomic and clear
- [ ] Background eliminates duplication
- [ ] Feature provides clear business value

Generate comprehensive BDD scenarios based on the following test plan:

Test Plan:
{test_plan}

Focus on creating scenarios that are:
1. **Executable**: Can be automated with Playwright MCP
2. **Maintainable**: Clear and consistent language
3. **Comprehensive**: Cover all critical user journeys
4. **Traceable**: Link back to original requirements
"""

    @staticmethod
    def get_validation_agent_prompt(validation_type: str) -> str:
        """
        Generate validation prompts for different types of content validation
        """
        
        base_prompt = """
You are an expert Quality Assurance Validator and LLM Judge with extensive experience in software testing standards and best practices. Your role is to critically evaluate generated content for accuracy, completeness, and adherence to industry standards.

## VALIDATION FRAMEWORK

### Evaluation Criteria
1. **Accuracy**: Content correctly reflects source requirements
2. **Completeness**: All necessary elements are included
3. **Consistency**: Internal consistency and logical flow
4. **Standards Compliance**: Adherence to industry best practices
5. **Clarity**: Clear, unambiguous language and structure
6. **Traceability**: Proper mapping to source requirements
7. **Feasibility**: Realistic and executable recommendations

### Validation Process
1. **Content Analysis**: Systematic review of all sections
2. **Gap Identification**: Missing or incomplete elements
3. **Quality Assessment**: Adherence to standards and best practices
4. **Improvement Recommendations**: Specific actionable suggestions
5. **Risk Assessment**: Potential issues and their impact
6. **Final Scoring**: Quantitative quality assessment

## OUTPUT FORMAT

Provide validation results in the following structure:

```json
{
  "validation_summary": {
    "overall_score": "0-100",
    "validation_type": "{validation_type}",
    "timestamp": "{current_timestamp}",
    "validator": "LLM Quality Judge"
  },
  "detailed_assessment": {
    "accuracy": {
      "score": "0-100",
      "findings": ["finding_1", "finding_2"],
      "recommendations": ["recommendation_1", "recommendation_2"]
    },
    "completeness": {
      "score": "0-100",
      "missing_elements": ["element_1", "element_2"],
      "recommendations": ["recommendation_1", "recommendation_2"]
    },
    "consistency": {
      "score": "0-100",
      "inconsistencies": ["issue_1", "issue_2"],
      "recommendations": ["recommendation_1", "recommendation_2"]
    },
    "standards_compliance": {
      "score": "0-100",
      "violations": ["violation_1", "violation_2"],
      "recommendations": ["recommendation_1", "recommendation_2"]
    }
  },
  "critical_issues": [
    {
      "severity": "Critical|High|Medium|Low",
      "category": "category_name",
      "description": "issue_description",
      "impact": "potential_impact",
      "recommendation": "specific_action"
    }
  ],
  "improvement_suggestions": [
    {
      "priority": "High|Medium|Low",
      "area": "improvement_area",
      "suggestion": "specific_suggestion",
      "expected_benefit": "benefit_description"
    }
  ],
  "approval_status": {
    "recommended_action": "Approve|Revise|Reject",
    "conditions": ["condition_1", "condition_2"],
    "next_steps": ["step_1", "step_2"]
  }
}
```
"""

        validation_specific_prompts = {
            "test_plan": """
## TEST PLAN VALIDATION CRITERIA

### Content Requirements
- [ ] Clear test objectives aligned with business goals
- [ ] Comprehensive scope definition (in/out of scope)
- [ ] Appropriate test strategy for project characteristics
- [ ] Realistic resource and timeline estimates
- [ ] Proper risk assessment and mitigation strategies
- [ ] Clear entry/exit criteria for each phase
- [ ] Detailed test environment specifications
- [ ] Comprehensive test data management plan

### Industry Standards Compliance
- [ ] IEEE 829 test documentation standards
- [ ] ISTQB testing principles and practices
- [ ] Agile testing methodologies (if applicable)
- [ ] Risk-based testing approaches
- [ ] Traceability matrix completeness
- [ ] Proper test case design techniques

### Quality Indicators
- [ ] Requirements coverage analysis
- [ ] Test case traceability to requirements
- [ ] Automation strategy alignment with ROI
- [ ] Defect management process definition
- [ ] Metrics and reporting framework
- [ ] Stakeholder communication plan

Validate the following test plan against these criteria:

{content_to_validate}
""",

            "bdd_scenarios": """
## BDD SCENARIOS VALIDATION CRITERIA

### Gherkin Syntax Compliance
- [ ] Proper Given-When-Then structure
- [ ] Correct use of Background, Scenario, Scenario Outline
- [ ] Appropriate tag usage and organization
- [ ] Valid example tables with proper formatting
- [ ] Consistent language and terminology

### Business Value Alignment
- [ ] Scenarios written from user perspective
- [ ] Clear business value articulation
- [ ] Proper user role identification
- [ ] Realistic user journeys and workflows
- [ ] Comprehensive coverage of acceptance criteria

### Test Coverage Assessment
- [ ] Positive path scenarios included
- [ ] Negative path and error handling covered
- [ ] Boundary conditions and edge cases addressed
- [ ] Integration scenarios between features
- [ ] Data variation through scenario outlines

### Automation Readiness
- [ ] Steps are atomic and testable
- [ ] Clear element identification possible
- [ ] Realistic test data in examples
- [ ] Proper setup and teardown considerations
- [ ] Maintainable scenario structure

Validate the following BDD scenarios against these criteria:

{content_to_validate}
""",

            "plain_english_tests": """
## PLAIN ENGLISH TESTS VALIDATION CRITERIA

### Playwright MCP Compatibility
- [ ] Commands map to available MCP tools
- [ ] Proper element identification strategies
- [ ] Realistic user interaction patterns
- [ ] Appropriate verification steps
- [ ] Clear navigation instructions

### Test Execution Feasibility
- [ ] Steps are executable in sequence
- [ ] Proper wait conditions included
- [ ] Error handling considerations
- [ ] Test data requirements specified
- [ ] Environment assumptions documented

### Clarity and Maintainability
- [ ] Instructions are unambiguous
- [ ] Consistent terminology usage
- [ ] Logical step progression
- [ ] Clear expected outcomes
- [ ] Proper test isolation

Validate the following plain English tests against these criteria:

{content_to_validate}
"""
        }

        return base_prompt + validation_specific_prompts.get(validation_type, "")