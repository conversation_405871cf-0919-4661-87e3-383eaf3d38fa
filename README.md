# 🤖 Agentic Test Automation Platform

An AI-powered test automation platform that follows SDLC and Agile practices, integrating RAG-based knowledge management with Playwright MCP for seamless test execution.

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Streamlit](https://img.shields.io/badge/Streamlit-1.29+-red.svg)](https://streamlit.io)
[![Groq](https://img.shields.io/badge/Groq-API-green.svg)](https://groq.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🌟 Features

- **Dual Knowledge Base**: Separate knowledge bases for application under test and test infrastructure
- **AI-Powered Test Generation**: Uses Groq with Kimi-K2-Instruct model for intelligent test planning
- **BDD Integration**: Generates proper Gherkin scenarios with tags and example tables
- **RAG-Enhanced**: FAISS-based vector search for contextual test generation
- **Playwright MCP Integration**: Plain English test execution via Playwright MCP
- **Interactive UI**: Streamlit-based interface with step-by-step workflow
- **Document Processing**: Supports PDF, DOCX, and PPTX file uploads

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Streamlit UI  │    │   AI Agent       │    │ Playwright MCP  │
│                 │    │ (Groq + Kimi)    │    │                 │
└─────────┬───────┘    └─────────┬────────┘    └─────────┬───────┘
          │                      │                       │
          │                      │                       │
┌─────────▼───────┐    ┌─────────▼────────┐    ┌─────────▼───────┐
│ Document        │    │ Knowledge Base   │    │ Test Execution  │
│ Processor       │    │ (FAISS + RAG)    │    │ & Reporting     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Node.js 18+ (for Playwright MCP)
- Groq API key

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/myselfsaurabh/agentic-test-automation.git
   cd agentic-test-automation
   ```

2. **Install Node.js** (required for Playwright MCP):
   ```bash
   # Download from https://nodejs.org/ (version 18+)
   # Or using package managers:
   
   # macOS (Homebrew)
   brew install node
   
   # Ubuntu/Debian
   sudo apt update && sudo apt install nodejs npm
   
   # Windows (Chocolatey)
   choco install nodejs
   ```

3. **Setup Python environment**:
   ```bash
   # Create virtual environment
   python -m venv venv
   
   # Activate virtual environment
   # Windows:
   venv\Scripts\activate
   # macOS/Linux:
   source venv/bin/activate
   
   # Install dependencies
   pip install -r requirements.txt
   ```

4. **Configure environment**:
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit .env file and add your Groq API key
   # Get your API key from: https://console.groq.com/keys
   GROQ_API_KEY=your_actual_groq_api_key_here
   ```

5. **Run the application**:
   ```bash
   streamlit run app.py
   ```

6. **Open browser**: Navigate to `http://localhost:8501`

## 📋 Usage Workflow

### 1. Knowledge Base Setup
- Upload application documentation (requirements, UI specs, etc.)
- Upload test infrastructure documents (standards, processes, historical data)
- System processes documents and builds FAISS vector indices

### 2. User Story Input
- Enter user story in any format (Gherkin, traditional, or free text)
- AI analyzes story complexity, risk level, and test areas

### 3. Test Plan Generation
- AI generates comprehensive test plan using RAG context
- Includes objectives, environment setup, preconditions, test data requirements
- Review and modify generated plan
- Approve to proceed

### 4. BDD Scenario Creation
- AI converts test plan to BDD scenarios with proper Gherkin syntax
- Includes tags (@smoke, @regression, @critical, etc.)
- Features example tables for data-driven testing
- Review and modify scenarios

### 5. Test Execution
- AI converts BDD scenarios to plain English commands
- Integrates with Playwright MCP for browser automation
- Real-time test execution and reporting
- Detailed test reports with pass/fail metrics

## 🔧 Configuration

### Environment Variables
```bash
# Required
GROQ_API_KEY=your_groq_api_key

# Optional
GROQ_MODEL=moonshotai/kimi-k2-instruct
MCP_SERVER_URL=localhost:8000
```

### File Structure
```
agentic-test-automation/
├── app.py                 # Main Streamlit application
├── config.py             # Configuration settings
├── requirements.txt      # Python dependencies
├── setup.py             # Setup script
├── src/
│   ├── __init__.py
│   ├── document_processor.py  # Document upload and processing
│   ├── knowledge_base.py      # FAISS-based RAG system
│   ├── ai_agent.py           # Groq AI integration
│   └── playwright_mcp.py     # Playwright MCP client
└── data/
    ├── knowledge_base/       # FAISS indices and metadata
    ├── processed_docs/       # Processed document storage
    └── reports/             # Generated test reports
```

## 🧠 AI Agent Capabilities

- **Test Plan Generation**: Creates comprehensive test plans with environment setup, preconditions, and data requirements
- **BDD Scenario Creation**: Generates proper Gherkin scenarios with tags and example tables
- **Plain English Conversion**: Converts BDD to natural language commands for Playwright MCP
- **Content Regeneration**: Allows iterative improvement based on user feedback
- **User Story Analysis**: Extracts complexity, risk level, and test areas from user stories

## 📚 Knowledge Base Features

- **Dual Storage**: Separate indices for application and test infrastructure knowledge
- **Vector Search**: FAISS-based similarity search for relevant context retrieval
- **Document Chunking**: Intelligent text chunking with overlap for better retrieval
- **Multi-format Support**: PDF, DOCX, and PPTX document processing
- **Persistent Storage**: Automatic saving and loading of indices

## 🎭 Playwright MCP Integration

- **Plain English Commands**: Natural language test instructions
- **Real-time Execution**: Live test execution with step-by-step feedback
- **Detailed Reporting**: Comprehensive test results with timing and error details
- **Test Suite Management**: Execute individual tests or complete suites

## 🔍 Example Usage

### User Story Input
```
As a user
I want to login to the application
So that I can access my dashboard

Acceptance Criteria:
- User can enter valid credentials
- System validates credentials
- User is redirected to dashboard on success
- Error message shown for invalid credentials
```

### Generated BDD Scenario
```gherkin
@login @smoke @critical
Feature: User Authentication
  As a user
  I want to login to the application
  So that I can access my dashboard

  @positive
  Scenario Outline: Successful login with valid credentials
    Given I am on the login page
    When I enter "<username>" and "<password>"
    And I click the login button
    Then I should be redirected to the dashboard
    And I should see a welcome message

    Examples:
      | username    | password    |
      | testuser1   | password123 |
      | admin       | admin123    |
```

### Plain English Commands
```
Test: Login with valid credentials
1. Navigate to the login page
2. Enter "testuser1" in the username field
3. Enter "password123" in the password field
4. Click the login button
5. Verify that the dashboard page is displayed
6. Verify that a welcome message is shown
```

## 🛠️ Development

### Adding New Document Types
1. Extend `DocumentProcessor` class in `src/document_processor.py`
2. Add extraction method for new file type
3. Update `SUPPORTED_FILE_TYPES` in `config.py`

### Customizing AI Prompts
1. Modify prompt templates in `src/ai_agent.py`
2. Adjust temperature and max_tokens for different creativity levels
3. Add new generation methods as needed

### Extending MCP Integration
1. Update `PlaywrightMCPClient` in `src/playwright_mcp.py`
2. Add new command types and execution patterns
3. Enhance error handling and reporting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed description

## 🔮 Roadmap

- [ ] Database integration (PostgreSQL/MongoDB)
- [ ] Advanced test data generation
- [ ] CI/CD pipeline integration
- [ ] Multi-browser parallel execution
- [ ] Visual regression testing
- [ ] API testing capabilities
- [ ] Performance testing integration
- [ ] Team collaboration features