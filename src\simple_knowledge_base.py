"""
Simple Knowledge Base without FAISS dependency
Uses basic text search and similarity
"""

import os
import json
import re
from typing import List, Dict, Any, Optional
import streamlit as st

class SimpleKnowledgeBase:
    """Simple knowledge base using text-based search"""
    
    def __init__(self):
        # Document storage
        self.app_documents = []
        self.test_documents = []
        
        # Storage paths
        self.storage_path = "data/knowledge_base"
        os.makedirs(self.storage_path, exist_ok=True)
        
        # Load existing documents
        self._load_documents()
    
    def add_documents(self, documents: List[Dict[str, Any]], doc_type: str = "app"):
        """Add documents to the knowledge base"""
        if doc_type == "app":
            self._add_to_app_knowledge(documents)
        elif doc_type == "test":
            self._add_to_test_knowledge(documents)
        else:
            raise ValueError("doc_type must be 'app' or 'test'")
    
    def _add_to_app_knowledge(self, documents: List[Dict[str, Any]]):
        """Add documents to application knowledge base"""
        # Chunk documents into smaller pieces for better retrieval
        chunks = self._chunk_documents(documents)
        
        if not chunks:
            return
        
        # Store document chunks
        self.app_documents.extend(chunks)
        
        # Save to disk
        self._save_documents()
        
        st.success(f"Added {len(chunks)} chunks to application knowledge base")
    
    def _add_to_test_knowledge(self, documents: List[Dict[str, Any]]):
        """Add documents to test infrastructure knowledge base"""
        chunks = self._chunk_documents(documents)
        
        if not chunks:
            return
        
        self.test_documents.extend(chunks)
        self._save_documents()
        
        st.success(f"Added {len(chunks)} chunks to test infrastructure knowledge base")
    
    def _chunk_documents(self, documents: List[Dict[str, Any]], chunk_size: int = 500, overlap: int = 50) -> List[Dict[str, Any]]:
        """Split documents into smaller chunks for better retrieval"""
        chunks = []
        
        for doc in documents:
            content = doc['content']
            words = content.split()
            
            # Create overlapping chunks
            for i in range(0, len(words), chunk_size - overlap):
                chunk_words = words[i:i + chunk_size]
                chunk_text = ' '.join(chunk_words)
                
                if len(chunk_text.strip()) > 50:  # Skip very small chunks
                    chunk = {
                        'text': chunk_text,
                        'source_filename': doc['filename'],
                        'doc_type': doc['doc_type'],
                        'chunk_id': f"{doc['filename']}_{i}"
                    }
                    chunks.append(chunk)
        
        return chunks
    
    def search(self, query: str, doc_type: str = "both", top_k: int = 5) -> List[Dict[str, Any]]:
        """Search the knowledge base for relevant documents using simple text matching"""
        query_lower = query.lower()
        query_words = set(re.findall(r'\w+', query_lower))
        
        results = []
        
        # Search in appropriate document collections
        search_docs = []
        if doc_type in ["app", "both"]:
            search_docs.extend(self.app_documents)
        if doc_type in ["test", "both"]:
            search_docs.extend(self.test_documents)
        
        # Calculate simple similarity scores
        for doc in search_docs:
            doc_text_lower = doc['text'].lower()
            doc_words = set(re.findall(r'\w+', doc_text_lower))
            
            # Calculate word overlap score
            common_words = query_words.intersection(doc_words)
            if common_words:
                # Simple scoring: ratio of common words
                score = len(common_words) / len(query_words) if query_words else 0
                
                # Boost score if query appears as phrase
                if query_lower in doc_text_lower:
                    score += 0.5
                
                result = doc.copy()
                result['score'] = score
                results.append(result)
        
        # Sort by relevance score and return top_k
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:top_k]
    
    def get_context_for_query(self, query: str, doc_type: str = "both", max_context_length: int = 2000) -> str:
        """Get relevant context for a query, formatted for LLM consumption"""
        relevant_docs = self.search(query, doc_type)
        
        context_parts = []
        current_length = 0
        
        for doc in relevant_docs:
            doc_text = f"Source: {doc['source_filename']}\n{doc['text']}\n---\n"
            
            if current_length + len(doc_text) > max_context_length:
                break
            
            context_parts.append(doc_text)
            current_length += len(doc_text)
        
        return "\n".join(context_parts)
    
    def _save_documents(self):
        """Save documents to disk"""
        try:
            # Save document metadata
            with open(os.path.join(self.storage_path, "app_documents.json"), 'w') as f:
                json.dump(self.app_documents, f, indent=2)
            
            with open(os.path.join(self.storage_path, "test_documents.json"), 'w') as f:
                json.dump(self.test_documents, f, indent=2)
                
        except Exception as e:
            st.error(f"Error saving knowledge base: {str(e)}")
    
    def _load_documents(self):
        """Load existing documents from disk"""
        try:
            # Load document metadata
            app_docs_path = os.path.join(self.storage_path, "app_documents.json")
            if os.path.exists(app_docs_path):
                with open(app_docs_path, 'r') as f:
                    self.app_documents = json.load(f)
            
            test_docs_path = os.path.join(self.storage_path, "test_documents.json")
            if os.path.exists(test_docs_path):
                with open(test_docs_path, 'r') as f:
                    self.test_documents = json.load(f)
                    
        except Exception as e:
            st.warning(f"Could not load existing knowledge base: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get knowledge base statistics"""
        return {
            'app_documents': len(self.app_documents),
            'test_documents': len(self.test_documents),
            'app_index_size': len(self.app_documents),
            'test_index_size': len(self.test_documents)
        }