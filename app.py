import streamlit as st
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import our custom modules
from src.document_processor import DocumentProcessor
from src.simple_knowledge_base import SimpleKnowledgeBase as KnowledgeBase
from src.ai_agent import AIAgent
from src.playwright_mcp import PlaywrightMCPClient

# Page config
st.set_page_config(
    page_title="Agentic Test Automation Platform",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'current_step' not in st.session_state:
    st.session_state.current_step = 'knowledge_base'
if 'user_story' not in st.session_state:
    st.session_state.user_story = ""
if 'test_plan' not in st.session_state:
    st.session_state.test_plan = ""
if 'test_scenarios' not in st.session_state:
    st.session_state.test_scenarios = ""
if 'plain_english_tests' not in st.session_state:
    st.session_state.plain_english_tests = ""
if 'prd_analysis' not in st.session_state:
    st.session_state.prd_analysis = None
if 'validation_results' not in st.session_state:
    st.session_state.validation_results = None

# Initialize components
@st.cache_resource
def init_components():
    doc_processor = DocumentProcessor()
    knowledge_base = KnowledgeBase()
    ai_agent = AIAgent()
    ai_agent.set_knowledge_base(knowledge_base)
    playwright_client = PlaywrightMCPClient()
    
    return doc_processor, knowledge_base, ai_agent, playwright_client

doc_processor, knowledge_base, ai_agent, playwright_client = init_components()

def main():
    st.title("🤖 Agentic Test Automation Platform")
    st.markdown("*AI-Powered BDD Test Generation with Playwright MCP*")
    
    # Sidebar navigation
    with st.sidebar:
        st.header("Navigation")
        step = st.selectbox(
            "Select Step:",
            ["Knowledge Base", "User Story", "Test Plan", "Test Scenarios", "Test Execution"],
            index=["Knowledge Base", "User Story", "Test Plan", "Test Scenarios", "Test Execution"].index(
                st.session_state.current_step.replace('_', ' ').title()
            )
        )
        st.session_state.current_step = step.lower().replace(' ', '_')
        
        st.markdown("---")
        st.markdown("### Progress")
        progress_steps = {
            "knowledge_base": "📚 Knowledge Base",
            "user_story": "📝 User Story", 
            "test_plan": "📋 Test Plan",
            "test_scenarios": "🎯 Test Scenarios",
            "test_execution": "🚀 Test Execution"
        }
        
        for key, label in progress_steps.items():
            if key == st.session_state.current_step:
                st.markdown(f"**➤ {label}**")
            else:
                st.markdown(f"   {label}")
    
    # Main content area
    if st.session_state.current_step == 'knowledge_base':
        show_knowledge_base()
    elif st.session_state.current_step == 'user_story':
        show_user_story()
    elif st.session_state.current_step == 'test_plan':
        show_test_plan()
    elif st.session_state.current_step == 'test_scenarios':
        show_test_scenarios()
    elif st.session_state.current_step == 'test_execution':
        show_test_execution()

def show_knowledge_base():
    st.header("📚 Knowledge Base Management")
    
    # Display current knowledge base stats
    stats = knowledge_base.get_stats()
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("App Documents", stats['app_documents'])
    with col2:
        st.metric("Test Documents", stats['test_documents'])
    with col3:
        st.metric("App Index Size", stats['app_index_size'])
    with col4:
        st.metric("Test Index Size", stats['test_index_size'])
    
    st.markdown("---")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Application Under Test Knowledge")
        st.markdown("Upload documents about the application being tested")
        
        app_files = st.file_uploader(
            "Upload Application Documents",
            type=['pdf', 'docx', 'pptx'],
            accept_multiple_files=True,
            key="app_docs"
        )
        
        if app_files:
            for file in app_files:
                st.success(f"Uploaded: {file.name}")
    
    with col2:
        st.subheader("Test Infrastructure Knowledge")
        st.markdown("Upload testing processes, standards, and historical data")
        
        test_files = st.file_uploader(
            "Upload Test Infrastructure Documents", 
            type=['pdf', 'docx', 'pptx'],
            accept_multiple_files=True,
            key="test_docs"
        )
        
        if test_files:
            for file in test_files:
                st.success(f"Uploaded: {file.name}")
    
    if st.button("Process Knowledge Base", type="primary"):
        with st.spinner("Processing documents and building knowledge base..."):
            processed_count = 0
            
            # Process application documents
            if app_files:
                app_docs = doc_processor.process_uploaded_files(app_files, "app")
                if app_docs:
                    knowledge_base.add_documents(app_docs, "app")
                    processed_count += len(app_docs)
                    
                    # Check if any document looks like a PRD and analyze it
                    for doc in app_docs:
                        if any(keyword in doc['filename'].lower() for keyword in ['prd', 'requirements', 'specification']):
                            with st.spinner("Analyzing PRD document..."):
                                prd_analysis = ai_agent.analyze_prd_document(doc['content'])
                                st.session_state.prd_analysis = prd_analysis
                                st.success(f"PRD analysis completed for {doc['filename']}")
                                
                                # Show brief analysis summary
                                if 'project_overview' in prd_analysis:
                                    st.info(f"**Project**: {prd_analysis['project_overview'].get('purpose', 'N/A')}")
                                break
            
            # Process test infrastructure documents
            if test_files:
                test_docs = doc_processor.process_uploaded_files(test_files, "test")
                if test_docs:
                    knowledge_base.add_documents(test_docs, "test")
                    processed_count += len(test_docs)
            
            if processed_count > 0:
                st.success(f"Knowledge base updated successfully! Processed {processed_count} documents.")
            else:
                st.warning("No documents to process. Please upload files first.")
    
    # Knowledge base search test
    st.markdown("---")
    st.subheader("🔍 Test Knowledge Base Search")
    search_query = st.text_input("Search Query", placeholder="Enter a search term to test the knowledge base")
    
    if search_query:
        with st.spinner("Searching..."):
            results = knowledge_base.search(search_query, "both", 3)
            
            if results:
                st.success(f"Found {len(results)} relevant documents:")
                for i, result in enumerate(results, 1):
                    with st.expander(f"{i}. {result['source_filename']} (Score: {result['score']:.3f})"):
                        st.write(result['text'][:500] + "..." if len(result['text']) > 500 else result['text'])
            else:
                st.info("No relevant documents found.")

def show_user_story():
    st.header("📝 User Story Input")
    
    st.markdown("Enter your user story in any format (Gherkin, traditional, or free text)")
    
    user_story = st.text_area(
        "User Story",
        value=st.session_state.user_story,
        height=200,
        placeholder="""Example:
As a user
I want to login to the application
So that I can access my dashboard

Acceptance Criteria:
- User can enter valid credentials
- System validates credentials
- User is redirected to dashboard on success
- Error message shown for invalid credentials"""
    )
    
    st.session_state.user_story = user_story
    
    if st.button("Generate Test Plan", type="primary", disabled=not user_story.strip()):
        with st.spinner("Analyzing user story and generating test plan..."):
            # Analyze user story first
            analysis = ai_agent.analyze_user_story(user_story)
            
            # Display analysis
            st.subheader("📊 User Story Analysis")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.info(f"**Type**: {analysis['story_type']}")
                st.info(f"**Complexity**: {analysis['complexity']}")
            with col2:
                st.info(f"**Risk Level**: {analysis['risk_level']}")
                st.info(f"**Effort**: {analysis['estimated_test_effort']}")
            with col3:
                st.info(f"**Test Areas**: {', '.join(analysis['test_areas'])}")
            
            # Generate test plan with PRD analysis if available
            generated_plan = ai_agent.generate_test_plan(user_story, st.session_state.prd_analysis)
            st.session_state.test_plan = generated_plan
            
            st.success("Test plan generated! Navigate to Test Plan tab to review.")
            st.session_state.current_step = 'test_plan'
            st.rerun()

def show_test_plan():
    st.header("📋 Test Plan Review")
    
    if not st.session_state.user_story:
        st.warning("Please complete the User Story step first.")
        return
    
    st.markdown("### Generated Test Plan")
    st.markdown("Review and modify the AI-generated test plan below:")
    
    test_plan = st.text_area(
        "Test Plan",
        value=st.session_state.test_plan,
        height=400
    )
    
    st.session_state.test_plan = test_plan
    
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("Regenerate Test Plan"):
            with st.spinner("Regenerating test plan..."):
                regenerated_plan = ai_agent.regenerate_content("test_plan", test_plan)
                st.session_state.test_plan = regenerated_plan
                st.success("Test plan regenerated!")
                st.rerun()
    
    with col2:
        feedback = st.text_input("Feedback for regeneration", placeholder="Optional: specific improvements needed")
        if st.button("Regenerate with Feedback") and feedback:
            with st.spinner("Regenerating with feedback..."):
                regenerated_plan = ai_agent.regenerate_content("test_plan", test_plan, feedback)
                st.session_state.test_plan = regenerated_plan
                st.success("Test plan regenerated with feedback!")
                st.rerun()
    
    with col3:
        if st.button("Validate Test Plan"):
            with st.spinner("Validating test plan with AI judge..."):
                validation_result = ai_agent.validate_test_plan(test_plan, st.session_state.prd_analysis)
                
                # Display validation results
                st.subheader("🔍 Test Plan Validation Results")
                
                if "validation_summary" in validation_result:
                    score = validation_result["validation_summary"].get("overall_score", 0)
                    
                    if score >= 90:
                        st.success(f"✅ Excellent quality! Score: {score}/100")
                    elif score >= 75:
                        st.info(f"✅ Good quality! Score: {score}/100")
                    elif score >= 60:
                        st.warning(f"⚠️ Acceptable quality. Score: {score}/100")
                    else:
                        st.error(f"❌ Needs improvement. Score: {score}/100")
                    
                    # Show critical issues if any
                    if "critical_issues" in validation_result and validation_result["critical_issues"]:
                        st.error("**Critical Issues Found:**")
                        for issue in validation_result["critical_issues"][:3]:
                            st.error(f"- {issue.get('description', 'Unknown issue')}")
                    
                    # Show improvement suggestions
                    if "improvement_suggestions" in validation_result and validation_result["improvement_suggestions"]:
                        st.info("**Improvement Suggestions:**")
                        for suggestion in validation_result["improvement_suggestions"][:3]:
                            st.info(f"- {suggestion.get('suggestion', 'No suggestion')}")
    
    # Approve button (separate row)
    if st.button("Approve Test Plan", type="primary"):
        st.success("Test plan approved! Proceeding to scenario generation.")
        st.session_state.current_step = 'test_scenarios'
        st.rerun()

def show_test_scenarios():
    st.header("🎯 Test Scenarios")
    
    if not st.session_state.test_plan:
        st.warning("Please complete the Test Plan step first.")
        return
    
    st.markdown("### Generated BDD Test Scenarios")
    st.markdown("Review and modify the BDD scenarios with tags and examples:")
    
    # Generate scenarios if not already done
    if not st.session_state.test_scenarios and st.session_state.test_plan:
        with st.spinner("Generating BDD scenarios..."):
            generated_scenarios = ai_agent.generate_bdd_scenarios(st.session_state.user_story, st.session_state.test_plan)
            st.session_state.test_scenarios = generated_scenarios
    
    test_scenarios = st.text_area(
        "BDD Test Scenarios",
        value=st.session_state.test_scenarios,
        height=500
    )
    
    st.session_state.test_scenarios = test_scenarios
    
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("Regenerate Scenarios"):
            with st.spinner("Regenerating BDD scenarios..."):
                regenerated_scenarios = ai_agent.regenerate_content("bdd_scenarios", test_scenarios)
                st.session_state.test_scenarios = regenerated_scenarios
                st.success("Scenarios regenerated!")
                st.rerun()
    
    with col2:
        scenario_feedback = st.text_input("Scenario feedback", placeholder="Optional: specific improvements needed")
        if st.button("Regenerate with Feedback", key="scenario_feedback") and scenario_feedback:
            with st.spinner("Regenerating with feedback..."):
                regenerated_scenarios = ai_agent.regenerate_content("bdd_scenarios", test_scenarios, scenario_feedback)
                st.session_state.test_scenarios = regenerated_scenarios
                st.success("Scenarios regenerated with feedback!")
                st.rerun()
    
    with col3:
        if st.button("Validate BDD Scenarios"):
            with st.spinner("Validating BDD scenarios with AI judge..."):
                validation_result = ai_agent.validate_bdd_scenarios(test_scenarios, st.session_state.test_plan)
                
                # Display validation results
                st.subheader("🔍 BDD Scenarios Validation Results")
                
                if "validation_summary" in validation_result:
                    score = validation_result["validation_summary"].get("overall_score", 0)
                    
                    if score >= 90:
                        st.success(f"✅ Excellent Gherkin quality! Score: {score}/100")
                    elif score >= 75:
                        st.info(f"✅ Good Gherkin quality! Score: {score}/100")
                    elif score >= 60:
                        st.warning(f"⚠️ Acceptable quality. Score: {score}/100")
                    else:
                        st.error(f"❌ Gherkin needs improvement. Score: {score}/100")
                    
                    # Show critical issues
                    if "critical_issues" in validation_result and validation_result["critical_issues"]:
                        st.error("**Critical Issues Found:**")
                        for issue in validation_result["critical_issues"][:3]:
                            st.error(f"- {issue.get('description', 'Unknown issue')}")
    
    # Approve button (separate row)
    if st.button("Approve Scenarios", type="primary"):
        st.success("Scenarios approved! Ready for test execution.")
        st.session_state.current_step = 'test_execution'
        st.rerun()

def show_test_execution():
    st.header("🚀 Test Execution with Playwright MCP")
    
    if not st.session_state.test_scenarios:
        st.warning("Please complete the Test Scenarios step first.")
        return
    
    st.markdown("### Execute Tests via Playwright MCP")
    st.markdown("The system will convert BDD scenarios to plain English commands for Playwright MCP")
    
    # Convert BDD to plain English if not already done
    if not st.session_state.plain_english_tests:
        with st.spinner("Converting BDD scenarios to plain English commands..."):
            plain_english = ai_agent.convert_bdd_to_plain_english(st.session_state.test_scenarios)
            st.session_state.plain_english_tests = plain_english
    
    # Show converted plain English commands
    st.subheader("Plain English Test Commands")
    
    plain_english_tests = st.text_area(
        "Plain English Commands",
        value=st.session_state.plain_english_tests,
        height=400
    )
    st.session_state.plain_english_tests = plain_english_tests
    
    # MCP Server Status
    st.markdown("---")
    col1, col2 = st.columns(2)
    with col1:
        mcp_status = playwright_client.check_mcp_server()
        status_color = "🟢" if mcp_status else "🔴"
        st.markdown(f"**Playwright MCP Status**: {status_color} {'Connected' if mcp_status else 'Disconnected'}")
        
        if not mcp_status:
            if st.button("Start MCP Server"):
                with st.spinner("Starting Playwright MCP server..."):
                    if playwright_client.start_mcp_server():
                        st.success("MCP server started successfully!")
                        st.rerun()
                    else:
                        st.error("Failed to start MCP server")
    
    with col2:
        if st.button("Refresh MCP Status"):
            st.rerun()
    
    # Comprehensive Validation Section
    st.markdown("---")
    st.subheader("🔍 Complete Workflow Validation")
    
    if st.button("Validate Complete Workflow", type="secondary"):
        with st.spinner("Running comprehensive validation with AI judges..."):
            validation_results = ai_agent.validate_complete_workflow(
                st.session_state.test_plan,
                st.session_state.test_scenarios,
                plain_english_tests,
                st.session_state.prd_analysis
            )
            
            st.session_state.validation_results = validation_results
            
            # Display overall results
            workflow_validation = validation_results.get("workflow_validation", {})
            overall_score = workflow_validation.get("overall_score", 0)
            overall_status = workflow_validation.get("overall_status", "unknown")
            
            st.markdown("### 📊 Overall Workflow Quality")
            
            if overall_status == "excellent":
                st.success(f"🎉 Excellent! Overall Score: {overall_score:.1f}/100")
            elif overall_status == "good":
                st.info(f"✅ Good quality! Overall Score: {overall_score:.1f}/100")
            elif overall_status == "acceptable":
                st.warning(f"⚠️ Acceptable quality. Overall Score: {overall_score:.1f}/100")
            else:
                st.error(f"❌ Needs significant improvement. Overall Score: {overall_score:.1f}/100")
            
            # Component-wise validation results
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.markdown("**Test Plan**")
                tp_score = validation_results.get("test_plan_validation", {}).get("validation_summary", {}).get("overall_score", 0)
                st.metric("Quality Score", f"{tp_score}/100")
            
            with col2:
                st.markdown("**BDD Scenarios**")
                bdd_score = validation_results.get("bdd_scenarios_validation", {}).get("validation_summary", {}).get("overall_score", 0)
                st.metric("Quality Score", f"{bdd_score}/100")
            
            with col3:
                st.markdown("**Plain English Tests**")
                pe_score = validation_results.get("plain_english_tests_validation", {}).get("validation_summary", {}).get("overall_score", 0)
                st.metric("Quality Score", f"{pe_score}/100")
            
            # Show top recommendations
            recommendations = validation_results.get("workflow_recommendations", [])
            if recommendations:
                st.markdown("### 💡 Top Recommendations")
                for i, rec in enumerate(recommendations[:5], 1):
                    priority = rec.get("priority", "Medium")
                    component = rec.get("component", "Unknown")
                    
                    if rec.get("type") == "critical_issue":
                        st.error(f"{i}. **{component}** ({priority}): {rec.get('issue', '')}")
                        st.error(f"   💡 {rec.get('recommendation', '')}")
                    else:
                        st.info(f"{i}. **{component}** ({priority}): {rec.get('suggestion', '')}")
    
    # Test Execution Controls
    st.markdown("---")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("Execute All Tests", type="primary"):
            with st.spinner("Executing tests via Playwright MCP..."):
                results = playwright_client.execute_test_suite(plain_english_tests)
                
                st.subheader("🎯 Test Results")
                for result in results:
                    status_emoji = "✅" if result["status"] == "passed" else "❌"
                    with st.expander(f"{status_emoji} {result['test_name']} - {result['status'].upper()}"):
                        st.write(f"**Execution Time**: {result.get('execution_time', 0):.2f}s")
                        
                        if result["status"] == "failed":
                            st.error(f"**Error**: {result.get('error_message', 'Unknown error')}")
                        
                        if "details" in result:
                            st.write("**Step Details**:")
                            for step in result["details"]:
                                step_emoji = "✅" if step["status"] == "passed" else "❌"
                                st.write(f"{step_emoji} {step['step']} ({step['duration']}s)")
                                if step["status"] == "failed" and "error" in step:
                                    st.error(f"   Error: {step['error']}")
    
    with col2:
        if st.button("Execute Single Test"):
            # Allow user to select and execute a single test
            st.info("Single test execution - select test from dropdown (feature coming soon)")
    
    with col3:
        if st.button("Clear Results"):
            playwright_client.clear_results()
            st.success("Results cleared!")
    
    # Test Results Summary
    summary = playwright_client.get_test_results_summary()
    if summary['total_tests'] > 0:
        st.markdown("---")
        st.subheader("📊 Test Summary")
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Tests", summary['total_tests'])
        with col2:
            st.metric("Passed", summary['passed'])
        with col3:
            st.metric("Failed", summary['failed'])
        with col4:
            st.metric("Pass Rate", f"{summary['pass_rate']}%")
        
        # Generate and display test report
        if st.button("Generate Detailed Report"):
            report = playwright_client.generate_test_report()
            st.markdown("### 📋 Detailed Test Report")
            st.markdown(report)
            
            # Option to download report
            import datetime
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            st.download_button(
                label="Download Report",
                data=report,
                file_name=f"test_report_{timestamp}.md",
                mime="text/markdown"
            )

if __name__ == "__main__":
    main()