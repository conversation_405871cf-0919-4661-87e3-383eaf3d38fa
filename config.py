import os
from pathlib import Path

# API Configuration
GROQ_API_KEY = os.getenv("GROQ_API_KEY", "")
GROQ_MODEL = "moonshotai/kimi-k2-instruct"

# Storage Configuration
DATA_DIR = Path("data")
KNOWLEDGE_BASE_DIR = DATA_DIR / "knowledge_base"
PROCESSED_DOCS_DIR = DATA_DIR / "processed_docs"
REPORTS_DIR = DATA_DIR / "reports"

# Create directories if they don't exist
for directory in [DATA_DIR, KNOWLEDGE_BASE_DIR, PROCESSED_DOCS_DIR, REPORTS_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# Embedding Model Configuration
EMBEDDING_MODEL = "all-MiniLM-L6-v2"
EMBEDDING_DIMENSION = 384

# Document Processing Configuration
SUPPORTED_FILE_TYPES = ['.pdf', '.docx', '.pptx']
MAX_FILE_SIZE_MB = 50
CHUNK_SIZE = 500
CHUNK_OVERLAP = 50

# RAG Configuration
MAX_CONTEXT_LENGTH = 2000
TOP_K_RESULTS = 5

# Playwright MCP Configuration
MCP_SERVER_URL = "localhost:8000"  # Adjust based on your MCP server setup
MCP_TIMEOUT = 30

# UI Configuration
PAGE_TITLE = "Agentic Test Automation Platform"
PAGE_ICON = "🤖"

# Test Execution Configuration
DEFAULT_BROWSERS = ["chrome", "firefox", "safari"]
TEST_TIMEOUT = 30
MAX_RETRIES = 3