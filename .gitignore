# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
data/
*.log
*.sqlite
*.db

# Streamlit
.streamlit/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# API Keys and Secrets
config/secrets.json
secrets.json
api_keys.json

# Test outputs
test_reports/
screenshots/
traces/

# Node.js (for Playwright MCP)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*