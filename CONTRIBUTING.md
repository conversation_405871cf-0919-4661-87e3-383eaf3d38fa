# Contributing to Agentic Test Automation Platform

Thank you for your interest in contributing! This document provides guidelines for contributing to the project.

## 🚀 Getting Started

1. Fork the repository
2. Clone your fork: `git clone https://github.com/yourusername/agentic-test-automation.git`
3. Create a feature branch: `git checkout -b feature/your-feature-name`
4. Make your changes
5. Test your changes
6. Commit your changes: `git commit -m "Add your feature"`
7. Push to your fork: `git push origin feature/your-feature-name`
8. Create a Pull Request

## 🛠️ Development Setup

1. **Environment Setup**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # or venv\Scripts\activate on Windows
   pip install -r requirements.txt
   ```

2. **Environment Variables**:
   ```bash
   cp .env.example .env
   # Add your GROQ_API_KEY to .env
   ```

3. **Run Tests**:
   ```bash
   python demo.py  # Test core components
   streamlit run app.py  # Test full application
   ```

## 📝 Code Style

- Follow PEP 8 for Python code
- Use meaningful variable and function names
- Add docstrings to functions and classes
- Keep functions focused and small
- Use type hints where appropriate

## 🧪 Testing

- Test your changes thoroughly
- Add unit tests for new functionality
- Ensure existing tests pass
- Test with different PRD documents
- Verify Playwright MCP integration

## 📋 Pull Request Guidelines

- Provide a clear description of changes
- Reference any related issues
- Include screenshots for UI changes
- Ensure code passes all tests
- Update documentation if needed

## 🐛 Bug Reports

When reporting bugs, please include:
- Steps to reproduce
- Expected behavior
- Actual behavior
- Environment details (OS, Python version, etc.)
- Error messages or logs

## 💡 Feature Requests

For feature requests, please provide:
- Clear description of the feature
- Use case and benefits
- Possible implementation approach
- Any relevant examples

## 📚 Areas for Contribution

- **Prompt Engineering**: Improve AI prompts for better outputs
- **Validation Agents**: Enhance LLM-as-Judge validation logic
- **Playwright MCP**: Improve browser automation integration
- **Knowledge Base**: Enhance RAG and document processing
- **UI/UX**: Improve Streamlit interface and user experience
- **Documentation**: Add tutorials, examples, and guides
- **Testing**: Add comprehensive test coverage

## 🤝 Code of Conduct

- Be respectful and inclusive
- Focus on constructive feedback
- Help others learn and grow
- Maintain a positive environment

## 📞 Questions?

Feel free to open an issue for questions or reach out to the maintainers.

Thank you for contributing! 🎉